from django.urls import path
from . import views

app_name = 'purchases'

urlpatterns = [
    # لوحة التحكم
    path('', views.purchases_dashboard, name='dashboard'),

    # إدارة الموردين
    path('suppliers/', views.supplier_list, name='supplier_list'),
    path('suppliers/create/', views.supplier_create, name='supplier_create'),
    path('suppliers/<int:pk>/', views.supplier_detail, name='supplier_detail'),
    path('suppliers/<int:pk>/edit/', views.supplier_edit, name='supplier_edit'),
    path('suppliers/<int:pk>/delete/', views.supplier_delete, name='supplier_delete'),
    path('suppliers/<int:supplier_id>/statement/', views.supplier_statement, name='supplier_statement'),

    # إدارة أوامر الشراء
    path('purchase-orders/', views.order_list, name='purchase_order_list'),
    path('purchase-orders/create/', views.order_create, name='purchase_order_create'),
    path('purchase-orders/<int:pk>/', views.order_detail, name='purchase_order_detail'),
    path('purchase-orders/<int:pk>/edit/', views.order_edit, name='purchase_order_edit'),
    path('purchase-orders/<int:pk>/delete/', views.order_delete, name='purchase_order_delete'),
    path('purchase-orders/<int:pk>/print/', views.order_print, name='purchase_order_print'),

    # إدارة فواتير الشراء
    path('purchase-invoices/', views.invoice_list, name='purchase_invoice_list'),
    path('purchase-invoices/create/', views.invoice_create, name='purchase_invoice_create'),
    path('purchase-invoices/<int:pk>/', views.invoice_detail, name='purchase_invoice_detail'),
    path('purchase-invoices/<int:pk>/edit/', views.invoice_edit, name='purchase_invoice_edit'),
    path('purchase-invoices/<int:pk>/delete/', views.invoice_delete, name='purchase_invoice_delete'),
    path('purchase-invoices/<int:pk>/add-payment/', views.add_payment, name='add_payment'),

    # المتابعة المالية
    path('financial-tracking/', views.financial_tracking, name='financial_tracking'),
    path('purchase-invoices/<int:pk>/print/', views.invoice_print, name='purchase_invoice_print'),
    path('purchase-invoices/<int:pk>/apply-to-stock/', views.apply_invoice_to_stock, name='apply_invoice_to_stock'),

    # التقارير
    path('reports/', views.reports_dashboard, name='reports'),
    path('reports/suppliers/', views.supplier_report, name='supplier_report'),
    path('reports/purchases/', views.purchase_report, name='purchase_report'),
    path('reports/inventory/', views.purchase_inventory_report, name='purchase_inventory_report'),
    path('reports/financial/', views.financial_report, name='financial_report'),
    path('reports/suppliers-financial/', views.suppliers_financial_summary, name='suppliers_financial_summary'),

    # دفعات الموردين المتقدمة
    path('suppliers/<int:supplier_id>/payment/create/', views.supplier_payment_create, name='supplier_payment_create'),

    # التنبيهات والإشعارات
    path('alerts/', views.alerts_dashboard, name='alerts_dashboard'),
    path('alerts/<int:alert_id>/', views.alert_detail, name='alert_detail'),

    # الموافقات
    path('approvals/', views.payment_approvals, name='payment_approvals'),
    path('approvals/<int:approval_id>/', views.approve_payment, name='approve_payment'),

    # تقييم المخاطر
    path('suppliers/<int:supplier_id>/risk-assessment/', views.supplier_risk_assessment, name='supplier_risk_assessment'),

    # توقعات التدفق النقدي
    path('cash-flow-forecast/', views.cash_flow_forecast, name='cash_flow_forecast'),
]
