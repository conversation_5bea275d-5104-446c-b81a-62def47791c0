from django.urls import path
from . import views

app_name = 'sales'

urlpatterns = [
    # لوحة التحكم
    path('', views.sales_dashboard, name='dashboard'),

    # إدارة العملاء
    path('customers/', views.customer_list, name='customer_list'),
    path('customers/create/', views.customer_create, name='customer_create'),
    path('customers/<int:pk>/edit/', views.customer_edit, name='customer_edit'),
    path('customers/<int:pk>/delete/', views.customer_delete, name='customer_delete'),

    # إدارة المنتجات
    path('products/', views.product_list, name='product_list'),
    path('products/create/', views.product_create, name='product_create'),
    path('products/<int:pk>/edit/', views.product_edit, name='product_edit'),
    path('products/<int:pk>/delete/', views.product_delete, name='product_delete'),

    # إدارة أوامر البيع
    path('orders/', views.order_list, name='order_list'),
    path('orders/create/', views.order_create, name='order_create'),
    path('orders/<int:pk>/edit/', views.order_edit, name='order_edit'),
    path('orders/<int:pk>/delete/', views.order_delete, name='order_delete'),

    # إدارة فواتير البيع
    path('invoices/', views.invoice_list, name='invoice_list'),
    path('invoices/create/', views.invoice_create, name='invoice_create'),
    path('invoices/create-advanced/', views.invoice_create_advanced, name='invoice_create_advanced'),
    path('invoices/<int:pk>/', views.invoice_detail, name='invoice_detail'),
    path('invoices/<int:pk>/edit/', views.invoice_edit, name='invoice_edit'),
    path('invoices/<int:pk>/delete/', views.invoice_delete, name='invoice_delete'),
    path('invoices/<int:pk>/pdf/', views.invoice_print_pdf, name='invoice_print_pdf'),

    # إدارة المناديب
    path('representatives/', views.representative_list, name='representative_list'),
    path('representatives/create/', views.representative_create, name='representative_create'),
    path('representatives/<int:pk>/edit/', views.representative_edit, name='representative_edit'),

    # إدارة السيارات
    path('vehicles/', views.vehicle_list, name='vehicle_list'),
    path('vehicles/create/', views.vehicle_create, name='vehicle_create'),
    path('vehicles/<int:pk>/edit/', views.vehicle_edit, name='vehicle_edit'),

    # إدارة تحميل السيارات
    path('vehicle-loading/', views.vehicle_loading_list, name='vehicle_loading_list'),
    path('vehicle-loading/create/', views.vehicle_loading_create, name='vehicle_loading_create'),
    path('vehicle-loading/<int:pk>/', views.vehicle_loading_detail, name='vehicle_loading_detail'),
    path('vehicle-loading/<int:pk>/edit/', views.vehicle_loading_edit, name='vehicle_loading_edit'),
    path('vehicle-loading/<int:pk>/confirm/', views.vehicle_loading_confirm, name='vehicle_loading_confirm'),

    # إدارة الحركة اليومية
    path('daily-movements/', views.daily_movement_list, name='daily_movement_list'),
    path('daily-movements/create/', views.daily_movement_create, name='daily_movement_create'),
    path('daily-movements/<int:pk>/', views.daily_movement_detail, name='daily_movement_detail'),
    path('daily-movements/<int:pk>/edit/', views.daily_movement_edit, name='daily_movement_edit'),
    path('daily-movements/<int:pk>/close/', views.daily_movement_close, name='daily_movement_close'),
    path('daily-movements/<int:pk>/auto-calculate/', views.daily_movement_auto_calculate, name='daily_movement_auto_calculate'),

    # إدارة المرتجعات
    path('returns/', views.sales_return_list, name='sales_return_list'),
    path('returns/create/', views.sales_return_create, name='sales_return_create'),
    path('returns/<int:pk>/', views.sales_return_detail, name='sales_return_detail'),
    path('returns/<int:pk>/edit/', views.sales_return_edit, name='sales_return_edit'),
    path('returns/<int:pk>/approve/', views.sales_return_approve, name='sales_return_approve'),

    # إدارة أذونات الصرف
    path('dispense-permissions/', views.dispense_permission_list, name='dispense_permission_list'),
    path('dispense-permissions/create/', views.dispense_permission_create, name='dispense_permission_create'),
    path('dispense-permissions/<int:pk>/', views.dispense_permission_detail, name='dispense_permission_detail'),
    path('dispense-permissions/<int:pk>/edit/', views.dispense_permission_edit, name='dispense_permission_edit'),
    path('dispense-permissions/<int:pk>/approve/', views.dispense_permission_approve, name='dispense_permission_approve'),
    path('dispense-permissions/<int:pk>/print/', views.dispense_permission_print, name='dispense_permission_print'),

    # إدارة الجرد
    path('inventory/', views.inventory_list, name='inventory_list'),
    path('inventory/create/', views.inventory_create, name='inventory_create'),
    path('inventory/<int:pk>/', views.inventory_detail, name='inventory_detail'),
    path('inventory/<int:pk>/edit/', views.inventory_edit, name='inventory_edit'),
    path('inventory/<int:pk>/approve/', views.inventory_approve, name='inventory_approve'),
    path('inventory/<int:pk>/auto-load/', views.inventory_auto_load_products, name='inventory_auto_load_products'),

    # التقارير والتحليلات
    path('reports/', views.reports_dashboard, name='reports_dashboard'),
    path('reports/sales/', views.sales_report, name='sales_report'),
    path('reports/inventory/', views.inventory_report, name='inventory_report'),
    path('reports/representatives/', views.representative_performance_report, name='representative_performance_report'),

    # API
    path('api/product/<int:product_id>/', views.get_product_data, name='get_product_data'),
    path('api/customer/<int:customer_id>/', views.get_customer_data, name='get_customer_data'),
    path('api/product-price/<int:product_id>/', views.get_product_price, name='get_product_price'),
    path('api/invoice-items/<int:invoice_id>/', views.get_invoice_items, name='get_invoice_items'),
]
