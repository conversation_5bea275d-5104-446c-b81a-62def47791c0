{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل المورد - {{ supplier.name }} - نظام أوساريك{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 40px 30px;
        border-radius: 20px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .supplier-info-card {
        background: white;
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        border-left: 5px solid #28a745;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: white;
        padding: 25px;
        border-radius: 15px;
        text-align: center;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    }

    .stat-card.orders { border-left: 5px solid #ffc107; }
    .stat-card.invoices { border-left: 5px solid #17a2b8; }
    .stat-card.amount { border-left: 5px solid #6f42c1; }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 15px;
        font-size: 1.5rem;
        color: white;
    }

    .icon-orders { background: linear-gradient(45deg, #ffc107, #e0a800); }
    .icon-invoices { background: linear-gradient(45deg, #17a2b8, #138496); }
    .icon-amount { background: linear-gradient(45deg, #6f42c1, #5a32a3); }

    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 10px;
        color: #2c3e50;
    }

    .stat-label {
        color: #6c757d;
        font-size: 1rem;
        font-weight: 500;
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .info-item {
        display: flex;
        align-items: center;
        padding: 15px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .info-item:last-child {
        border-bottom: none;
    }

    .info-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(45deg, #28a745, #20c997);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 15px;
        color: white;
        font-size: 1.1rem;
    }

    .info-content h6 {
        margin: 0 0 5px 0;
        color: #6c757d;
        font-size: 0.9rem;
        font-weight: 600;
    }

    .info-content p {
        margin: 0;
        color: #2c3e50;
        font-weight: 500;
    }

    .recent-section {
        background: white;
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .section-title {
        font-size: 1.5rem;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #28a745;
    }

    .recent-item {
        display: flex;
        align-items: center;
        padding: 15px;
        border-radius: 10px;
        margin-bottom: 10px;
        transition: all 0.3s ease;
        border: 1px solid #f0f0f0;
    }

    .recent-item:hover {
        background: #f8f9fa;
        transform: translateX(5px);
    }

    .recent-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 15px;
        color: white;
        font-size: 1.2rem;
    }

    .recent-content h6 {
        margin: 0 0 5px 0;
        color: #2c3e50;
        font-weight: 600;
    }

    .recent-content p {
        margin: 0;
        color: #6c757d;
        font-size: 0.9rem;
    }

    .recent-date {
        margin-right: auto;
        color: #6c757d;
        font-size: 0.85rem;
    }

    .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        margin-right: 10px;
    }

    .status-draft { background: #f8d7da; color: #721c24; }
    .status-sent { background: #d1ecf1; color: #0c5460; }
    .status-confirmed { background: #d4edda; color: #155724; }
    .status-received { background: #d1ecf1; color: #0c5460; }
    .status-paid { background: #d4edda; color: #155724; }
    .status-partially_paid { background: #fff3cd; color: #856404; }
    .status-overdue { background: #f8d7da; color: #721c24; }

    .action-buttons {
        display: flex;
        gap: 15px;
        margin-top: 30px;
    }

    .btn-action {
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
    }

    .btn-edit {
        background: linear-gradient(45deg, #ffc107, #e0a800);
        color: white;
    }

    .btn-edit:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(255, 193, 7, 0.3);
        color: white;
    }

    .btn-delete {
        background: linear-gradient(45deg, #dc3545, #c82333);
        color: white;
    }

    .btn-delete:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(220, 53, 69, 0.3);
        color: white;
    }

    .btn-back {
        background: linear-gradient(45deg, #6c757d, #5a6268);
        color: white;
    }

    .btn-back:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(108, 117, 125, 0.3);
        color: white;
    }

    .empty-state {
        text-align: center;
        padding: 40px;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: 15px;
        opacity: 0.5;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-person-badge"></i>
                    تفاصيل المورد
                </h1>
                <p class="mb-0">{{ supplier.name }}</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'purchases:supplier_risk_assessment' supplier.pk %}" class="btn btn-warning me-2">
                    <i class="bi bi-shield-exclamation"></i>
                    تقييم المخاطر
                </a>
                <a href="{% url 'purchases:supplier_payment_create' supplier.pk %}" class="btn btn-success me-2">
                    <i class="bi bi-cash-stack"></i>
                    إضافة دفعة
                </a>
                <a href="{% url 'purchases:dashboard' %}" class="btn btn-outline-light me-2">
                    <i class="bi bi-house"></i>
                    لوحة التحكم
                </a>
                <a href="{% url 'purchases:supplier_list' %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-right"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <!-- معلومات المورد -->
    <div class="supplier-info-card">
        <div class="row">
            <div class="col-md-6">
                <h3 class="mb-4">
                    <i class="bi bi-info-circle"></i>
                    المعلومات الأساسية
                </h3>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="bi bi-person"></i>
                        </div>
                        <div class="info-content">
                            <h6>اسم المورد</h6>
                            <p>{{ supplier.name }}</p>
                        </div>
                    </div>
                    
                    {% if supplier.email %}
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="bi bi-envelope"></i>
                        </div>
                        <div class="info-content">
                            <h6>البريد الإلكتروني</h6>
                            <p><a href="mailto:{{ supplier.email }}">{{ supplier.email }}</a></p>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if supplier.phone %}
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="bi bi-telephone"></i>
                        </div>
                        <div class="info-content">
                            <h6>رقم الهاتف</h6>
                            <p><a href="tel:{{ supplier.phone }}">{{ supplier.phone }}</a></p>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if supplier.address %}
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="bi bi-geo-alt"></i>
                        </div>
                        <div class="info-content">
                            <h6>العنوان</h6>
                            <p>{{ supplier.address }}</p>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if supplier.tax_number %}
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="bi bi-receipt"></i>
                        </div>
                        <div class="info-content">
                            <h6>الرقم الضريبي</h6>
                            <p>{{ supplier.tax_number }}</p>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if supplier.payment_terms %}
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="bi bi-credit-card"></i>
                        </div>
                        <div class="info-content">
                            <h6>شروط الدفع</h6>
                            <p>{{ supplier.payment_terms }}</p>
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="bi bi-calendar"></i>
                        </div>
                        <div class="info-content">
                            <h6>تاريخ الإنشاء</h6>
                            <p>{{ supplier.created_at|date:"d/m/Y H:i" }}</p>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="bi bi-toggle-{{ supplier.is_active|yesno:'on,off' }}"></i>
                        </div>
                        <div class="info-content">
                            <h6>الحالة</h6>
                            <p>
                                <span class="status-badge {% if supplier.is_active %}status-confirmed{% else %}status-draft{% endif %}">
                                    {% if supplier.is_active %}نشط{% else %}غير نشط{% endif %}
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <!-- الإحصائيات -->
                <h3 class="mb-4">
                    <i class="bi bi-bar-chart"></i>
                    الإحصائيات
                </h3>
                <div class="stats-grid">
                    <div class="stat-card orders">
                        <div class="stat-icon icon-orders">
                            <i class="bi bi-cart-plus"></i>
                        </div>
                        <div class="stat-number">{{ total_orders|default:0 }}</div>
                        <div class="stat-label">أوامر الشراء</div>
                    </div>

                    <div class="stat-card invoices">
                        <div class="stat-icon icon-invoices">
                            <i class="bi bi-receipt"></i>
                        </div>
                        <div class="stat-number">{{ total_invoices|default:0 }}</div>
                        <div class="stat-label">فواتير الشراء</div>
                    </div>

                    <div class="stat-card amount">
                        <div class="stat-icon icon-amount">
                            <i class="bi bi-currency-exchange"></i>
                        </div>
                        <div class="stat-number">{{ total_purchases|floatformat:0|default:0 }}</div>
                        <div class="stat-label">إجمالي المشتريات (ج.م)</div>
                    </div>
                </div>

                <!-- الإحصائيات المالية المتقدمة -->
                <div class="row mt-4">
                    <div class="col-12">
                        <h4 class="mb-3">
                            <i class="bi bi-calculator"></i>
                            الإحصائيات المالية
                        </h4>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card" style="border-left-color: #28a745;">
                        <div class="stat-icon" style="background: linear-gradient(45deg, #28a745, #20c997);">
                            <i class="bi bi-check-circle"></i>
                        </div>
                        <div class="stat-number">{{ total_paid|floatformat:0|default:0 }}</div>
                        <div class="stat-label">إجمالي المدفوع (ج.م)</div>
                    </div>

                    <div class="stat-card" style="border-left-color: #dc3545;">
                        <div class="stat-icon" style="background: linear-gradient(45deg, #dc3545, #c82333);">
                            <i class="bi bi-exclamation-circle"></i>
                        </div>
                        <div class="stat-number">{{ total_remaining|floatformat:0|default:0 }}</div>
                        <div class="stat-label">إجمالي المتبقي (ج.م)</div>
                    </div>

                    <div class="stat-card" style="border-left-color: #ffc107;">
                        <div class="stat-icon" style="background: linear-gradient(45deg, #ffc107, #e0a800);">
                            <i class="bi bi-clock"></i>
                        </div>
                        <div class="stat-number">{{ overdue_amount|floatformat:0|default:0 }}</div>
                        <div class="stat-label">المبالغ المتأخرة (ج.m)</div>
                    </div>

                    <div class="stat-card" style="border-left-color: #17a2b8;">
                        <div class="stat-icon" style="background: linear-gradient(45deg, #17a2b8, #138496);">
                            <i class="bi bi-percent"></i>
                        </div>
                        <div class="stat-number">{{ payment_percentage|floatformat:1|default:0 }}%</div>
                        <div class="stat-label">نسبة السداد</div>
                    </div>
                </div>

                <!-- شريط التقدم للسداد -->
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="progress" style="height: 20px; border-radius: 10px;">
                            <div class="progress-bar bg-success" role="progressbar"
                                 style="width: {{ payment_percentage|floatformat:1 }}%;"
                                 aria-valuenow="{{ payment_percentage|floatformat:1 }}"
                                 aria-valuemin="0" aria-valuemax="100">
                                {{ payment_percentage|floatformat:1 }}% مدفوع
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات الفواتير حسب الحالة -->
                <div class="row mt-4">
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-success">{{ paid_invoices }}</div>
                            <small class="text-muted">فواتير مدفوعة</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-warning">{{ partially_paid_invoices }}</div>
                            <small class="text-muted">فواتير مدفوعة جزئياً</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-danger">{{ overdue_invoices }}</div>
                            <small class="text-muted">فواتير متأخرة</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-info">{{ other_invoices }}</div>
                            <small class="text-muted">فواتير أخرى</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- أزرار الإجراءات -->
        <div class="action-buttons">
            <a href="{% url 'purchases:supplier_edit' supplier.pk %}" class="btn-action btn-edit">
                <i class="bi bi-pencil"></i>
                تعديل المورد
            </a>
            <button class="btn-action btn-delete" onclick="deleteSupplier('{{ supplier.name }}', '{% url 'purchases:supplier_delete' supplier.pk %}')">
                <i class="bi bi-trash"></i>
                حذف المورد
            </button>
            <a href="{% url 'purchases:dashboard' %}" class="btn-action btn-back">
                <i class="bi bi-house"></i>
                لوحة التحكم
            </a>

            <a href="{% url 'purchases:supplier_list' %}" class="btn-action btn-back">
                <i class="bi bi-arrow-right"></i>
                العودة للقائمة
            </a>
        </div>
    </div>

    <div class="row">
        <!-- أحدث أوامر الشراء -->
        <div class="col-md-6">
            <div class="recent-section">
                <h3 class="section-title">
                    <i class="bi bi-cart-plus"></i>
                    أحدث أوامر الشراء
                </h3>
                {% if recent_orders %}
                    {% for order in recent_orders %}
                        <div class="recent-item">
                            <div class="recent-icon" style="background: linear-gradient(45deg, #ffc107, #e0a800);">
                                <i class="bi bi-cart-plus"></i>
                            </div>
                            <div class="recent-content">
                                <h6>{{ order.order_number }}</h6>
                                <p>{{ order.total_amount|floatformat:2 }} ج.م</p>
                            </div>
                            <span class="status-badge status-{{ order.status }}">
                                {{ order.get_status_display }}
                            </span>
                            <div class="recent-date">
                                {{ order.created_at|date:"d/m/Y" }}
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="empty-state">
                        <i class="bi bi-cart-x"></i>
                        <p>لا توجد أوامر شراء</p>
                    </div>
                {% endif %}
            </div>
        </div>
        
        <!-- أحدث فواتير الشراء -->
        <div class="col-md-6">
            <div class="recent-section">
                <h3 class="section-title">
                    <i class="bi bi-receipt"></i>
                    أحدث فواتير الشراء
                </h3>
                {% if recent_invoices %}
                    {% for invoice in recent_invoices %}
                        <div class="recent-item">
                            <div class="recent-icon" style="background: linear-gradient(45deg, #17a2b8, #138496);">
                                <i class="bi bi-receipt"></i>
                            </div>
                            <div class="recent-content">
                                <h6>{{ invoice.invoice_number }}</h6>
                                <p>{{ invoice.total_amount|floatformat:2 }} ج.م</p>
                            </div>
                            <span class="status-badge status-{{ invoice.status }}">
                                {{ invoice.get_status_display }}
                            </span>
                            <div class="recent-date">
                                {{ invoice.invoice_date|date:"d/m/Y" }}
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="empty-state">
                        <i class="bi bi-receipt-cutoff"></i>
                        <p>لا توجد فواتير شراء</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function deleteSupplier(supplierName, deleteUrl) {
        if (confirm(`هل تريد حذف المورد "${supplierName}"؟`)) {
            window.location.href = deleteUrl;
        }
    }

    // تأثيرات بصرية
    document.addEventListener('DOMContentLoaded', function() {
        const cards = document.querySelectorAll('.supplier-info-card, .recent-section, .stat-card');
        cards.forEach((card, index) => {
            setTimeout(() => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'all 0.6s ease';
                
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 100);
            }, index * 100);
        });
    });
</script>
{% endblock %}
