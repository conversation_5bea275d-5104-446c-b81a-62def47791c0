from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Sum, Count, Q, F, Avg, Max, Min
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator
from django.template.loader import get_template
from datetime import datetime, timedelta
import json

# استيراد النماذج الجديدة
from .models import (
    Customer, Product, SalesOrder, SalesOrderItem, SalesInvoice, SalesInvoiceItem,
    SalesRepresentative, Vehicle, VehicleLoading, VehicleLoadingItem,
    DailyMovement, SalesReturn, SalesReturnItem, DispensePermission, DispensePermissionItem,
    Inventory, InventoryItem, ProductMovement, Payment
)

# استيراد النماذج الجديدة
from .forms import (
    CustomerForm, ProductForm, SalesOrderForm, SalesOrderItemFormSet,
    SalesInvoiceForm, SalesInvoiceItemFormSet, SalesRepresentativeForm, VehicleForm,
    VehicleLoadingForm, VehicleLoadingItemFormSet, DailyMovementForm,
    SalesReturnForm, SalesReturnItemFormSet, DispensePermissionForm, DispensePermissionItemFormSet,
    InventoryForm, InventoryItemFormSet, PaymentForm
)

# استيراد مكتبات PDF
try:
    from weasyprint import HTML, CSS
    from django.template.loader import render_to_string
    WEASYPRINT_AVAILABLE = True
except ImportError:
    WEASYPRINT_AVAILABLE = False

@login_required
def sales_dashboard(request):
    """لوحة تحكم المبيعات المتقدمة"""
    # إحصائيات عامة
    total_customers = Customer.objects.filter(is_active=True).count()
    total_products = Product.objects.filter(is_active=True).count()
    total_orders = SalesOrder.objects.count()
    total_invoices = SalesInvoice.objects.count()
    total_representatives = SalesRepresentative.objects.filter(is_active=True).count()
    total_vehicles = Vehicle.objects.filter(is_active=True).count()

    # إحصائيات مالية
    today = datetime.now().date()
    paid_invoices = SalesInvoice.objects.filter(status='paid')
    total_sales = sum(invoice.total_amount for invoice in paid_invoices)
    pending_invoices = SalesInvoice.objects.filter(status='sent').count()
    overdue_invoices = SalesInvoice.objects.filter(
        status='overdue', due_date__lt=today).count()

    # إحصائيات اليوم
    today_sales = SalesInvoice.objects.filter(invoice_date=today, status='paid').aggregate(
        total=Sum('total_amount'))['total'] or 0
    today_orders = SalesOrder.objects.filter(order_date=today).count()

    # إحصائيات الشهر
    month_start = today.replace(day=1)
    month_sales = SalesInvoice.objects.filter(
        invoice_date__gte=month_start, status='paid').aggregate(
        total=Sum('total_amount'))['total'] or 0

    # إحصائيات أنواع العملاء
    wholesale_customers = Customer.objects.filter(customer_type='wholesale', is_active=True).count()
    retail_customers = Customer.objects.filter(customer_type='retail', is_active=True).count()
    credit_customers = Customer.objects.filter(customer_type='credit', is_active=True).count()

    # إحصائيات أنواع الفواتير
    wholesale_invoices = SalesInvoice.objects.filter(invoice_type='wholesale').count()
    retail_invoices = SalesInvoice.objects.filter(invoice_type='retail').count()
    credit_invoices = SalesInvoice.objects.filter(invoice_type='credit').count()

    # أحدث الطلبات
    recent_orders = SalesOrder.objects.select_related('customer', 'representative').order_by('-created_at')[:5]

    # أحدث الفواتير
    recent_invoices = SalesInvoice.objects.select_related('customer', 'representative').order_by('-created_at')[:5]

    # أحدث المرتجعات
    recent_returns = SalesReturn.objects.select_related('customer', 'representative').order_by('-created_at')[:5]

    # أحدث المدفوعات
    recent_payments = Payment.objects.select_related('customer', 'representative').order_by('-created_at')[:5]

    context = {
        'total_customers': total_customers,
        'total_products': total_products,
        'total_orders': total_orders,
        'total_invoices': total_invoices,
        'total_representatives': total_representatives,
        'total_vehicles': total_vehicles,
        'total_sales': total_sales,
        'pending_invoices': pending_invoices,
        'overdue_invoices': overdue_invoices,
        'today_sales': today_sales,
        'today_orders': today_orders,
        'month_sales': month_sales,
        'wholesale_customers': wholesale_customers,
        'retail_customers': retail_customers,
        'credit_customers': credit_customers,
        'wholesale_invoices': wholesale_invoices,
        'retail_invoices': retail_invoices,
        'credit_invoices': credit_invoices,
        'recent_orders': recent_orders,
        'recent_invoices': recent_invoices,
        'recent_returns': recent_returns,
        'recent_payments': recent_payments,
    }
    return render(request, 'sales/simple_dashboard.html', context)

# ========== وظائف الفواتير المتقدمة ==========

@login_required
def invoice_create_advanced(request):
    """إنشاء فاتورة متقدمة"""
    if request.method == 'POST':
        form = SalesInvoiceForm(request.POST)
        formset = SalesInvoiceItemFormSet(request.POST)

        if form.is_valid() and formset.is_valid():
            invoice = form.save(commit=False)
            invoice.created_by = request.user

            # تحديد نوع الفاتورة حسب نوع العميل
            if not invoice.invoice_type:
                invoice.invoice_type = invoice.customer.customer_type

            # تحديد تاريخ الاستحقاق حسب نوع العميل
            if invoice.invoice_type == 'credit':
                invoice.due_date = invoice.invoice_date + timedelta(days=invoice.customer.credit_days)
            else:
                invoice.due_date = invoice.invoice_date

            invoice.save()

            # حفظ عناصر الفاتورة
            formset.instance = invoice
            items = formset.save(commit=False)

            for item in items:
                # تحديد السعر حسب نوع العميل
                item.unit_price = item.product.get_price_for_customer_type(invoice.customer.customer_type)
                item.save()

                # تحديث المخزون
                item.product.stock_quantity -= item.quantity
                item.product.save()

                # تسجيل حركة الصنف
                ProductMovement.objects.create(
                    product=item.product,
                    movement_type='sale',
                    movement_date=invoice.invoice_date,
                    quantity=item.quantity,
                    unit_price=item.unit_price,
                    reference_number=invoice.invoice_number,
                    reference_type='فاتورة بيع',
                    representative=invoice.representative
                )

            messages.success(request, f'تم إنشاء الفاتورة {invoice.invoice_number} بنجاح')
            return redirect('sales:invoice_detail', pk=invoice.pk)
    else:
        form = SalesInvoiceForm()
        formset = SalesInvoiceItemFormSet()

    context = {
        'form': form,
        'formset': formset,
        'title': 'إنشاء فاتورة جديدة'
    }
    return render(request, 'sales/invoice_form_advanced.html', context)

@login_required
def invoice_detail(request, pk):
    """تفاصيل الفاتورة"""
    invoice = get_object_or_404(SalesInvoice, pk=pk)
    context = {
        'invoice': invoice,
        'items': invoice.items.all(),
    }
    return render(request, 'sales/invoice_detail.html', context)

@login_required
def invoice_print_pdf(request, pk):
    """طباعة الفاتورة كـ PDF"""
    invoice = get_object_or_404(SalesInvoice, pk=pk)

    if not WEASYPRINT_AVAILABLE:
        messages.error(request, 'مكتبة PDF غير متوفرة')
        return redirect('sales:invoice_detail', pk=pk)

    # تحديث حالة الطباعة
    invoice.is_printed = True
    invoice.save()

    # إعداد البيانات للقالب
    context = {
        'invoice': invoice,
        'items': invoice.items.all(),
        'company_name': 'شركة أوساريك',
        'company_address': 'العنوان',
        'company_phone': 'رقم الهاتف',
    }

    # تحديد القالب حسب نوع الفاتورة
    template_name = f'sales/invoice_pdf_{invoice.invoice_type}.html'
    try:
        html_string = render_to_string(template_name, context)
    except:
        html_string = render_to_string('sales/invoice_pdf_default.html', context)

    # إنشاء PDF
    html = HTML(string=html_string)
    pdf = html.write_pdf()

    # إرجاع PDF كاستجابة
    response = HttpResponse(pdf, content_type='application/pdf')
    response['Content-Disposition'] = f'inline; filename="invoice_{invoice.invoice_number}.pdf"'

    return response

@login_required
def get_customer_data(request, customer_id):
    """الحصول على بيانات العميل عبر AJAX"""
    try:
        customer = Customer.objects.get(id=customer_id)
        data = {
            'customer_type': customer.customer_type,
            'credit_limit': float(customer.credit_limit),
            'credit_days': customer.credit_days,
            'current_balance': float(customer.current_balance),
            'assigned_representative': customer.assigned_representative.id if customer.assigned_representative else None,
        }
        return JsonResponse(data)
    except Customer.DoesNotExist:
        return JsonResponse({'error': 'العميل غير موجود'}, status=404)

@login_required
def get_product_price(request, product_id):
    """الحصول على سعر المنتج حسب نوع العميل"""
    try:
        product = Product.objects.get(id=product_id)
        customer_type = request.GET.get('customer_type', 'retail')

        price = product.get_price_for_customer_type(customer_type)

        data = {
            'unit_price': float(price),
            'stock_quantity': product.stock_quantity,
            'unit': product.unit,
        }
        return JsonResponse(data)
    except Product.DoesNotExist:
        return JsonResponse({'error': 'المنتج غير موجود'}, status=404)

# ========== إدارة العملاء ==========
@login_required
def customer_list(request):
    """قائمة العملاء"""
    search = request.GET.get('search', '')
    customers = Customer.objects.all()

    if search:
        customers = customers.filter(
            Q(name__icontains=search) |
            Q(email__icontains=search) |
            Q(phone__icontains=search)
        )

    paginator = Paginator(customers, 20)
    page_number = request.GET.get('page')
    customers = paginator.get_page(page_number)

    context = {
        'customers': customers,
        'search': search,
    }
    return render(request, 'sales/customer_list.html', context)

@login_required
def customer_create(request):
    """إضافة عميل جديد"""
    if request.method == 'POST':
        form = CustomerForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة العميل بنجاح')
            return redirect('sales:customer_list')
    else:
        form = CustomerForm()

    context = {'form': form, 'title': 'إضافة عميل جديد'}
    return render(request, 'sales/customer_form.html', context)

@login_required
def customer_edit(request, pk):
    """تعديل عميل"""
    customer = get_object_or_404(Customer, pk=pk)
    if request.method == 'POST':
        form = CustomerForm(request.POST, instance=customer)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث بيانات العميل بنجاح')
            return redirect('sales:customer_list')
    else:
        form = CustomerForm(instance=customer)

    context = {'form': form, 'title': 'تعديل العميل', 'customer': customer}
    return render(request, 'sales/customer_form.html', context)

@login_required
def customer_delete(request, pk):
    """حذف عميل"""
    customer = get_object_or_404(Customer, pk=pk)
    if request.method == 'POST':
        customer.delete()
        messages.success(request, 'تم حذف العميل بنجاح')
        return redirect('sales:customer_list')

    context = {'customer': customer}
    return render(request, 'sales/customer_confirm_delete.html', context)

# ========== إدارة المنتجات ==========
@login_required
def product_list(request):
    """قائمة المنتجات"""
    search = request.GET.get('search', '')
    products = Product.objects.all()

    if search:
        products = products.filter(
            Q(name__icontains=search) |
            Q(code__icontains=search)
        )

    paginator = Paginator(products, 20)
    page_number = request.GET.get('page')
    products = paginator.get_page(page_number)

    context = {
        'products': products,
        'search': search,
    }
    return render(request, 'sales/product_list.html', context)

@login_required
def product_create(request):
    """إضافة منتج جديد"""
    if request.method == 'POST':
        form = ProductForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة المنتج بنجاح')
            return redirect('sales:product_list')
    else:
        form = ProductForm()

    context = {'form': form, 'title': 'إضافة منتج جديد'}
    return render(request, 'sales/product_form.html', context)

@login_required
def product_edit(request, pk):
    """تعديل منتج"""
    product = get_object_or_404(Product, pk=pk)
    if request.method == 'POST':
        form = ProductForm(request.POST, instance=product)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث بيانات المنتج بنجاح')
            return redirect('sales:product_list')
    else:
        form = ProductForm(instance=product)

    context = {'form': form, 'title': 'تعديل المنتج', 'product': product}
    return render(request, 'sales/product_form.html', context)

@login_required
def product_delete(request, pk):
    """حذف منتج"""
    product = get_object_or_404(Product, pk=pk)
    if request.method == 'POST':
        product.delete()
        messages.success(request, 'تم حذف المنتج بنجاح')
        return redirect('sales:product_list')

    context = {'product': product}
    return render(request, 'sales/product_confirm_delete.html', context)

# ========== إدارة أوامر البيع ==========
@login_required
def order_list(request):
    """قائمة أوامر البيع"""
    search = request.GET.get('search', '')
    status = request.GET.get('status', '')
    orders = SalesOrder.objects.select_related('customer', 'created_by')

    if search:
        orders = orders.filter(
            Q(order_number__icontains=search) |
            Q(customer__name__icontains=search)
        )

    if status:
        orders = orders.filter(status=status)

    paginator = Paginator(orders, 20)
    page_number = request.GET.get('page')
    orders = paginator.get_page(page_number)

    context = {
        'orders': orders,
        'search': search,
        'status': status,
        'status_choices': SalesOrder.STATUS_CHOICES,
    }
    return render(request, 'sales/order_list.html', context)

@login_required
def order_create(request):
    """إضافة أمر بيع جديد"""
    if request.method == 'POST':
        form = SalesOrderForm(request.POST)
        formset = SalesOrderItemFormSet(request.POST)

        if form.is_valid() and formset.is_valid():
            order = form.save(commit=False)
            order.created_by = request.user
            # إنشاء رقم الطلب تلقائياً
            order.order_number = f"SO-{datetime.now().strftime('%Y%m%d')}-{SalesOrder.objects.count() + 1:04d}"
            order.save()

            formset.instance = order
            formset.save()

            messages.success(request, 'تم إضافة أمر البيع بنجاح')
            return redirect('sales:order_list')
    else:
        form = SalesOrderForm()
        formset = SalesOrderItemFormSet()

    context = {
        'form': form,
        'formset': formset,
        'title': 'إضافة أمر بيع جديد'
    }
    return render(request, 'sales/order_form.html', context)

@login_required
def order_edit(request, pk):
    """تعديل أمر بيع"""
    order = get_object_or_404(SalesOrder, pk=pk)
    if request.method == 'POST':
        form = SalesOrderForm(request.POST, instance=order)
        formset = SalesOrderItemFormSet(request.POST, instance=order)

        if form.is_valid() and formset.is_valid():
            form.save()
            formset.save()
            messages.success(request, 'تم تحديث أمر البيع بنجاح')
            return redirect('sales:order_list')
    else:
        form = SalesOrderForm(instance=order)
        formset = SalesOrderItemFormSet(instance=order)

    context = {
        'form': form,
        'formset': formset,
        'title': 'تعديل أمر البيع',
        'order': order
    }
    return render(request, 'sales/order_form.html', context)

@login_required
def order_delete(request, pk):
    """حذف أمر بيع"""
    order = get_object_or_404(SalesOrder, pk=pk)
    if request.method == 'POST':
        order.delete()
        messages.success(request, 'تم حذف أمر البيع بنجاح')
        return redirect('sales:order_list')

    context = {'order': order}
    return render(request, 'sales/order_confirm_delete.html', context)

# ========== إدارة فواتير البيع ==========
@login_required
def invoice_list(request):
    """قائمة فواتير البيع"""
    search = request.GET.get('search', '')
    status = request.GET.get('status', '')
    invoices = SalesInvoice.objects.select_related('customer', 'created_by')

    if search:
        invoices = invoices.filter(
            Q(invoice_number__icontains=search) |
            Q(customer__name__icontains=search)
        )

    if status:
        invoices = invoices.filter(status=status)

    paginator = Paginator(invoices, 20)
    page_number = request.GET.get('page')
    invoices = paginator.get_page(page_number)

    context = {
        'invoices': invoices,
        'search': search,
        'status': status,
        'status_choices': SalesInvoice.STATUS_CHOICES,
    }
    return render(request, 'sales/invoice_list.html', context)

@login_required
def invoice_create(request):
    """إضافة فاتورة بيع جديدة"""
    if request.method == 'POST':
        form = SalesInvoiceForm(request.POST)
        formset = SalesInvoiceItemFormSet(request.POST)

        if form.is_valid() and formset.is_valid():
            invoice = form.save(commit=False)
            invoice.created_by = request.user
            # إنشاء رقم الفاتورة تلقائياً
            invoice.invoice_number = f"INV-{datetime.now().strftime('%Y%m%d')}-{SalesInvoice.objects.count() + 1:04d}"
            invoice.save()

            formset.instance = invoice
            formset.save()

            messages.success(request, 'تم إضافة فاتورة البيع بنجاح')
            return redirect('sales:invoice_list')
    else:
        form = SalesInvoiceForm()
        formset = SalesInvoiceItemFormSet()

    context = {
        'form': form,
        'formset': formset,
        'title': 'إضافة فاتورة بيع جديدة'
    }
    return render(request, 'sales/invoice_form.html', context)

@login_required
def invoice_edit(request, pk):
    """تعديل فاتورة بيع"""
    invoice = get_object_or_404(SalesInvoice, pk=pk)
    if request.method == 'POST':
        form = SalesInvoiceForm(request.POST, instance=invoice)
        formset = SalesInvoiceItemFormSet(request.POST, instance=invoice)

        if form.is_valid() and formset.is_valid():
            form.save()
            formset.save()
            messages.success(request, 'تم تحديث فاتورة البيع بنجاح')
            return redirect('sales:invoice_list')
    else:
        form = SalesInvoiceForm(instance=invoice)
        formset = SalesInvoiceItemFormSet(instance=invoice)

    context = {
        'form': form,
        'formset': formset,
        'title': 'تعديل فاتورة البيع',
        'invoice': invoice
    }
    return render(request, 'sales/invoice_form.html', context)

@login_required
def invoice_delete(request, pk):
    """حذف فاتورة بيع"""
    invoice = get_object_or_404(SalesInvoice, pk=pk)
    if request.method == 'POST':
        invoice.delete()
        messages.success(request, 'تم حذف فاتورة البيع بنجاح')
        return redirect('sales:invoice_list')

    context = {'invoice': invoice}
    return render(request, 'sales/invoice_confirm_delete.html', context)

# ========== إدارة المناديب ==========

@login_required
def representative_list(request):
    """قائمة المناديب"""
    search = request.GET.get('search', '')
    representatives = SalesRepresentative.objects.select_related('user')

    if search:
        representatives = representatives.filter(
            Q(user__first_name__icontains=search) |
            Q(user__last_name__icontains=search) |
            Q(employee_id__icontains=search)
        )

    paginator = Paginator(representatives, 20)
    page_number = request.GET.get('page')
    representatives = paginator.get_page(page_number)

    context = {
        'representatives': representatives,
        'search': search,
    }
    return render(request, 'sales/representative_list.html', context)

@login_required
def representative_create(request):
    """إضافة مندوب جديد"""
    if request.method == 'POST':
        form = SalesRepresentativeForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة المندوب بنجاح')
            return redirect('sales:representative_list')
    else:
        form = SalesRepresentativeForm()

    context = {
        'form': form,
        'title': 'إضافة مندوب جديد'
    }
    return render(request, 'sales/representative_form.html', context)

@login_required
def representative_edit(request, pk):
    """تعديل مندوب"""
    representative = get_object_or_404(SalesRepresentative, pk=pk)
    if request.method == 'POST':
        form = SalesRepresentativeForm(request.POST, instance=representative)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث بيانات المندوب بنجاح')
            return redirect('sales:representative_list')
    else:
        form = SalesRepresentativeForm(instance=representative)

    context = {
        'form': form,
        'representative': representative,
        'title': 'تعديل بيانات المندوب'
    }
    return render(request, 'sales/representative_form.html', context)

# ========== إدارة السيارات ==========

@login_required
def vehicle_list(request):
    """قائمة السيارات"""
    search = request.GET.get('search', '')
    vehicles = Vehicle.objects.select_related('assigned_representative__user')

    if search:
        vehicles = vehicles.filter(
            Q(plate_number__icontains=search) |
            Q(brand__icontains=search) |
            Q(model__icontains=search)
        )

    paginator = Paginator(vehicles, 20)
    page_number = request.GET.get('page')
    vehicles = paginator.get_page(page_number)

    context = {
        'vehicles': vehicles,
        'search': search,
    }
    return render(request, 'sales/vehicle_list.html', context)

@login_required
def vehicle_create(request):
    """إضافة سيارة جديدة"""
    if request.method == 'POST':
        form = VehicleForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة السيارة بنجاح')
            return redirect('sales:vehicle_list')
    else:
        form = VehicleForm()

    context = {
        'form': form,
        'title': 'إضافة سيارة جديدة'
    }
    return render(request, 'sales/vehicle_form.html', context)

@login_required
def vehicle_edit(request, pk):
    """تعديل سيارة"""
    vehicle = get_object_or_404(Vehicle, pk=pk)
    if request.method == 'POST':
        form = VehicleForm(request.POST, instance=vehicle)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث بيانات السيارة بنجاح')
            return redirect('sales:vehicle_list')
    else:
        form = VehicleForm(instance=vehicle)

    context = {
        'form': form,
        'vehicle': vehicle,
        'title': 'تعديل بيانات السيارة'
    }
    return render(request, 'sales/vehicle_form.html', context)

# ========== إدارة تحميل السيارات ==========

@login_required
def vehicle_loading_list(request):
    """قائمة تحميل السيارات"""
    search = request.GET.get('search', '')
    status = request.GET.get('status', '')
    loadings = VehicleLoading.objects.select_related('vehicle', 'representative__user', 'created_by')

    if search:
        loadings = loadings.filter(
            Q(loading_number__icontains=search) |
            Q(vehicle__plate_number__icontains=search) |
            Q(representative__user__first_name__icontains=search) |
            Q(representative__user__last_name__icontains=search)
        )

    if status:
        loadings = loadings.filter(status=status)

    paginator = Paginator(loadings, 20)
    page_number = request.GET.get('page')
    loadings = paginator.get_page(page_number)

    context = {
        'loadings': loadings,
        'search': search,
        'status': status,
        'status_choices': VehicleLoading._meta.get_field('status').choices,
    }
    return render(request, 'sales/vehicle_loading_list.html', context)

@login_required
def vehicle_loading_create(request):
    """إنشاء تحميل سيارة جديد"""
    if request.method == 'POST':
        form = VehicleLoadingForm(request.POST)
        formset = VehicleLoadingItemFormSet(request.POST)

        if form.is_valid() and formset.is_valid():
            loading = form.save(commit=False)
            loading.created_by = request.user
            loading.save()

            # حفظ عناصر التحميل
            formset.instance = loading
            items = formset.save(commit=False)

            total_weight = 0
            total_value = 0

            for item in items:
                item.save()
                total_weight += item.total_weight
                total_value += item.total_value

                # تسجيل حركة الصنف
                ProductMovement.objects.create(
                    product=item.product,
                    movement_type='loading',
                    movement_date=loading.loading_date,
                    quantity=item.quantity,
                    unit_price=item.unit_price,
                    reference_number=loading.loading_number,
                    reference_type='تحميل سيارة',
                    warehouse=loading.warehouse,
                    representative=loading.representative
                )

            # تحديث إجماليات التحميل
            loading.total_weight = total_weight
            loading.total_value = total_value
            loading.save()

            messages.success(request, f'تم إنشاء تحميل السيارة {loading.loading_number} بنجاح')
            return redirect('sales:vehicle_loading_detail', pk=loading.pk)
    else:
        form = VehicleLoadingForm()
        formset = VehicleLoadingItemFormSet()

    context = {
        'form': form,
        'formset': formset,
        'title': 'إنشاء تحميل سيارة جديد'
    }
    return render(request, 'sales/vehicle_loading_form.html', context)

@login_required
def vehicle_loading_detail(request, pk):
    """تفاصيل تحميل السيارة"""
    loading = get_object_or_404(VehicleLoading, pk=pk)
    context = {
        'loading': loading,
        'items': loading.items.all(),
    }
    return render(request, 'sales/vehicle_loading_detail.html', context)

@login_required
def vehicle_loading_edit(request, pk):
    """تعديل تحميل السيارة"""
    loading = get_object_or_404(VehicleLoading, pk=pk)

    if loading.status != 'pending':
        messages.error(request, 'لا يمكن تعديل تحميل تم تأكيده')
        return redirect('sales:vehicle_loading_detail', pk=pk)

    if request.method == 'POST':
        form = VehicleLoadingForm(request.POST, instance=loading)
        formset = VehicleLoadingItemFormSet(request.POST, instance=loading)

        if form.is_valid() and formset.is_valid():
            form.save()
            formset.save()

            # إعادة حساب الإجماليات
            total_weight = sum(item.total_weight for item in loading.items.all())
            total_value = sum(item.total_value for item in loading.items.all())
            loading.total_weight = total_weight
            loading.total_value = total_value
            loading.save()

            messages.success(request, 'تم تحديث تحميل السيارة بنجاح')
            return redirect('sales:vehicle_loading_detail', pk=pk)
    else:
        form = VehicleLoadingForm(instance=loading)
        formset = VehicleLoadingItemFormSet(instance=loading)

    context = {
        'form': form,
        'formset': formset,
        'loading': loading,
        'title': 'تعديل تحميل السيارة'
    }
    return render(request, 'sales/vehicle_loading_form.html', context)

@login_required
def vehicle_loading_confirm(request, pk):
    """تأكيد تحميل السيارة"""
    loading = get_object_or_404(VehicleLoading, pk=pk)

    if loading.status != 'pending':
        messages.error(request, 'تم تأكيد هذا التحميل مسبقاً')
        return redirect('sales:vehicle_loading_detail', pk=pk)

    if request.method == 'POST':
        loading.status = 'loaded'
        loading.save()

        # تحديث المخزون
        for item in loading.items.all():
            item.product.stock_quantity -= item.quantity
            item.product.save()

        messages.success(request, 'تم تأكيد تحميل السيارة بنجاح')
        return redirect('sales:vehicle_loading_detail', pk=pk)

    context = {'loading': loading}
    return render(request, 'sales/vehicle_loading_confirm.html', context)

# ========== إدارة الحركة اليومية للمناديب ==========

@login_required
def daily_movement_list(request):
    """قائمة الحركات اليومية"""
    search = request.GET.get('search', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    representative = request.GET.get('representative', '')

    movements = DailyMovement.objects.select_related('representative__user', 'vehicle')

    if search:
        movements = movements.filter(
            Q(representative__user__first_name__icontains=search) |
            Q(representative__user__last_name__icontains=search) |
            Q(vehicle__plate_number__icontains=search)
        )

    if date_from:
        movements = movements.filter(movement_date__gte=date_from)

    if date_to:
        movements = movements.filter(movement_date__lte=date_to)

    if representative:
        movements = movements.filter(representative_id=representative)

    movements = movements.order_by('-movement_date', '-created_at')

    paginator = Paginator(movements, 20)
    page_number = request.GET.get('page')
    movements = paginator.get_page(page_number)

    # إحصائيات سريعة
    total_movements = DailyMovement.objects.count()
    today_movements = DailyMovement.objects.filter(movement_date=datetime.now().date()).count()
    open_movements = DailyMovement.objects.filter(is_closed=False).count()

    context = {
        'movements': movements,
        'search': search,
        'date_from': date_from,
        'date_to': date_to,
        'representative': representative,
        'representatives': SalesRepresentative.objects.filter(is_active=True),
        'total_movements': total_movements,
        'today_movements': today_movements,
        'open_movements': open_movements,
    }
    return render(request, 'sales/daily_movement_list.html', context)

@login_required
def daily_movement_create(request):
    """إنشاء حركة يومية جديدة"""
    if request.method == 'POST':
        form = DailyMovementForm(request.POST)
        if form.is_valid():
            movement = form.save()

            # حساب صافي الحركة تلقائياً
            movement.closing_cash = movement.net_movement
            movement.save()

            messages.success(request, 'تم إنشاء الحركة اليومية بنجاح')
            return redirect('sales:daily_movement_detail', pk=movement.pk)
    else:
        # تعيين القيم الافتراضية
        initial_data = {
            'movement_date': datetime.now().date(),
            'opening_cash': 0,
            'total_sales': 0,
            'total_collections': 0,
            'total_returns': 0,
            'expenses': 0,
        }
        form = DailyMovementForm(initial=initial_data)

    context = {
        'form': form,
        'title': 'إنشاء حركة يومية جديدة'
    }
    return render(request, 'sales/daily_movement_form.html', context)

@login_required
def daily_movement_detail(request, pk):
    """تفاصيل الحركة اليومية"""
    movement = get_object_or_404(DailyMovement, pk=pk)

    # الحصول على الفواتير المرتبطة بهذا اليوم والمندوب
    related_invoices = SalesInvoice.objects.filter(
        representative=movement.representative,
        invoice_date=movement.movement_date
    ).select_related('customer')

    # الحصول على المدفوعات المرتبطة
    related_payments = Payment.objects.filter(
        representative=movement.representative,
        payment_date=movement.movement_date
    ).select_related('customer')

    # الحصول على المرتجعات المرتبطة
    related_returns = SalesReturn.objects.filter(
        representative=movement.representative,
        return_date=movement.movement_date
    ).select_related('customer')

    context = {
        'movement': movement,
        'related_invoices': related_invoices,
        'related_payments': related_payments,
        'related_returns': related_returns,
    }
    return render(request, 'sales/daily_movement_detail.html', context)

@login_required
def daily_movement_edit(request, pk):
    """تعديل الحركة اليومية"""
    movement = get_object_or_404(DailyMovement, pk=pk)

    if movement.is_closed:
        messages.error(request, 'لا يمكن تعديل حركة مغلقة')
        return redirect('sales:daily_movement_detail', pk=pk)

    if request.method == 'POST':
        form = DailyMovementForm(request.POST, instance=movement)
        if form.is_valid():
            movement = form.save()

            # إعادة حساب صافي الحركة
            movement.closing_cash = movement.net_movement
            movement.save()

            messages.success(request, 'تم تحديث الحركة اليومية بنجاح')
            return redirect('sales:daily_movement_detail', pk=pk)
    else:
        form = DailyMovementForm(instance=movement)

    context = {
        'form': form,
        'movement': movement,
        'title': 'تعديل الحركة اليومية'
    }
    return render(request, 'sales/daily_movement_form.html', context)

@login_required
def daily_movement_close(request, pk):
    """إغلاق الحركة اليومية"""
    movement = get_object_or_404(DailyMovement, pk=pk)

    if movement.is_closed:
        messages.error(request, 'هذه الحركة مغلقة مسبقاً')
        return redirect('sales:daily_movement_detail', pk=pk)

    if request.method == 'POST':
        movement.is_closed = True
        movement.save()

        messages.success(request, 'تم إغلاق الحركة اليومية بنجاح')
        return redirect('sales:daily_movement_detail', pk=pk)

    context = {'movement': movement}
    return render(request, 'sales/daily_movement_close.html', context)

@login_required
def daily_movement_auto_calculate(request, pk):
    """حساب تلقائي للحركة اليومية من الفواتير والمدفوعات"""
    movement = get_object_or_404(DailyMovement, pk=pk)

    if movement.is_closed:
        return JsonResponse({'error': 'الحركة مغلقة'}, status=400)

    # حساب المبيعات من الفواتير
    sales_total = SalesInvoice.objects.filter(
        representative=movement.representative,
        invoice_date=movement.movement_date,
        status__in=['paid', 'sent']
    ).aggregate(total=Sum('total_amount'))['total'] or 0

    # حساب التحصيلات من المدفوعات
    collections_total = Payment.objects.filter(
        representative=movement.representative,
        payment_date=movement.movement_date,
        payment_type='collection'
    ).aggregate(total=Sum('amount'))['total'] or 0

    # حساب المرتجعات
    returns_total = SalesReturn.objects.filter(
        representative=movement.representative,
        return_date=movement.movement_date,
        is_approved=True
    ).aggregate(total=Sum('total_amount'))['total'] or 0

    # تحديث الحركة
    movement.total_sales = sales_total
    movement.total_collections = collections_total
    movement.total_returns = returns_total
    movement.closing_cash = movement.net_movement
    movement.save()

    return JsonResponse({
        'success': True,
        'total_sales': float(sales_total),
        'total_collections': float(collections_total),
        'total_returns': float(returns_total),
        'net_movement': float(movement.net_movement),
        'closing_cash': float(movement.closing_cash),
    })

# ========== إدارة المرتجعات ==========

@login_required
def sales_return_list(request):
    """قائمة مرتجعات المبيعات"""
    search = request.GET.get('search', '')
    status = request.GET.get('status', '')
    reason = request.GET.get('reason', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')

    returns = SalesReturn.objects.select_related('customer', 'representative__user', 'original_invoice', 'created_by')

    if search:
        returns = returns.filter(
            Q(return_number__icontains=search) |
            Q(customer__name__icontains=search) |
            Q(original_invoice__invoice_number__icontains=search)
        )

    if status == 'approved':
        returns = returns.filter(is_approved=True)
    elif status == 'pending':
        returns = returns.filter(is_approved=False)

    if reason:
        returns = returns.filter(reason=reason)

    if date_from:
        returns = returns.filter(return_date__gte=date_from)

    if date_to:
        returns = returns.filter(return_date__lte=date_to)

    returns = returns.order_by('-created_at')

    paginator = Paginator(returns, 20)
    page_number = request.GET.get('page')
    returns = paginator.get_page(page_number)

    context = {
        'returns': returns,
        'search': search,
        'status': status,
        'reason': reason,
        'date_from': date_from,
        'date_to': date_to,
        'reason_choices': SalesReturn.RETURN_REASONS,
    }
    return render(request, 'sales/sales_return_list.html', context)

@login_required
def sales_return_create(request):
    """إنشاء مرتجع جديد"""
    if request.method == 'POST':
        form = SalesReturnForm(request.POST)
        formset = SalesReturnItemFormSet(request.POST)

        if form.is_valid() and formset.is_valid():
            return_record = form.save(commit=False)
            return_record.created_by = request.user
            return_record.save()

            # حفظ عناصر المرتجع
            formset.instance = return_record
            items = formset.save(commit=False)

            total_amount = 0

            for item in items:
                item.save()
                total_amount += item.total_amount

                # تسجيل حركة الصنف
                ProductMovement.objects.create(
                    product=item.product,
                    movement_type='return',
                    movement_date=return_record.return_date,
                    quantity=item.quantity,
                    unit_price=item.unit_price,
                    reference_number=return_record.return_number,
                    reference_type='مرتجع مبيعات',
                    representative=return_record.representative
                )

            # تحديث إجمالي المرتجع
            return_record.total_amount = total_amount
            return_record.save()

            messages.success(request, f'تم إنشاء المرتجع {return_record.return_number} بنجاح')
            return redirect('sales:sales_return_detail', pk=return_record.pk)
    else:
        form = SalesReturnForm()
        formset = SalesReturnItemFormSet()

    context = {
        'form': form,
        'formset': formset,
        'title': 'إنشاء مرتجع جديد'
    }
    return render(request, 'sales/sales_return_form.html', context)

@login_required
def sales_return_detail(request, pk):
    """تفاصيل المرتجع"""
    return_record = get_object_or_404(SalesReturn, pk=pk)
    context = {
        'return_record': return_record,
        'items': return_record.items.all(),
    }
    return render(request, 'sales/sales_return_detail.html', context)

@login_required
def sales_return_edit(request, pk):
    """تعديل المرتجع"""
    return_record = get_object_or_404(SalesReturn, pk=pk)

    if return_record.is_approved:
        messages.error(request, 'لا يمكن تعديل مرتجع معتمد')
        return redirect('sales:sales_return_detail', pk=pk)

    if request.method == 'POST':
        form = SalesReturnForm(request.POST, instance=return_record)
        formset = SalesReturnItemFormSet(request.POST, instance=return_record)

        if form.is_valid() and formset.is_valid():
            form.save()
            formset.save()

            # إعادة حساب الإجمالي
            total_amount = sum(item.total_amount for item in return_record.items.all())
            return_record.total_amount = total_amount
            return_record.save()

            messages.success(request, 'تم تحديث المرتجع بنجاح')
            return redirect('sales:sales_return_detail', pk=pk)
    else:
        form = SalesReturnForm(instance=return_record)
        formset = SalesReturnItemFormSet(instance=return_record)

    context = {
        'form': form,
        'formset': formset,
        'return_record': return_record,
        'title': 'تعديل المرتجع'
    }
    return render(request, 'sales/sales_return_form.html', context)

@login_required
def sales_return_approve(request, pk):
    """اعتماد المرتجع"""
    return_record = get_object_or_404(SalesReturn, pk=pk)

    if return_record.is_approved:
        messages.error(request, 'هذا المرتجع معتمد مسبقاً')
        return redirect('sales:sales_return_detail', pk=pk)

    if request.method == 'POST':
        return_record.is_approved = True
        return_record.approved_by = request.user
        return_record.save()

        # تحديث المخزون
        for item in return_record.items.all():
            if item.condition == 'good':
                # إضافة للمخزون فقط إذا كان المنتج في حالة جيدة
                item.product.stock_quantity += item.quantity
                item.product.save()

        messages.success(request, 'تم اعتماد المرتجع بنجاح')
        return redirect('sales:sales_return_detail', pk=pk)

    context = {'return_record': return_record}
    return render(request, 'sales/sales_return_approve.html', context)

@login_required
def get_invoice_items(request, invoice_id):
    """الحصول على عناصر الفاتورة عبر AJAX"""
    try:
        invoice = SalesInvoice.objects.get(id=invoice_id)
        items = []

        for item in invoice.items.all():
            items.append({
                'product_id': item.product.id,
                'product_name': item.product.name,
                'quantity': float(item.quantity),
                'unit_price': float(item.unit_price),
                'unit': item.product.unit,
            })

        return JsonResponse({
            'success': True,
            'items': items,
            'customer_id': invoice.customer.id,
            'representative_id': invoice.representative.id if invoice.representative else None,
        })
    except SalesInvoice.DoesNotExist:
        return JsonResponse({'error': 'الفاتورة غير موجودة'}, status=404)

# ========== إدارة أذونات الصرف ==========

@login_required
def dispense_permission_list(request):
    """قائمة أذونات الصرف"""
    search = request.GET.get('search', '')
    status = request.GET.get('status', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    warehouse = request.GET.get('warehouse', '')

    permissions = DispensePermission.objects.select_related('representative__user', 'vehicle', 'created_by', 'approved_by')

    if search:
        permissions = permissions.filter(
            Q(permission_number__icontains=search) |
            Q(representative__user__first_name__icontains=search) |
            Q(representative__user__last_name__icontains=search) |
            Q(purpose__icontains=search)
        )

    if status == 'approved':
        permissions = permissions.filter(is_approved=True)
    elif status == 'pending':
        permissions = permissions.filter(is_approved=False)

    if warehouse:
        permissions = permissions.filter(warehouse__icontains=warehouse)

    if date_from:
        permissions = permissions.filter(dispense_date__gte=date_from)

    if date_to:
        permissions = permissions.filter(dispense_date__lte=date_to)

    permissions = permissions.order_by('-created_at')

    paginator = Paginator(permissions, 20)
    page_number = request.GET.get('page')
    permissions = paginator.get_page(page_number)

    # الحصول على قائمة المخازن المتاحة
    warehouses = DispensePermission.objects.values_list('warehouse', flat=True).distinct()

    context = {
        'permissions': permissions,
        'search': search,
        'status': status,
        'date_from': date_from,
        'date_to': date_to,
        'warehouse': warehouse,
        'warehouses': warehouses,
    }
    return render(request, 'sales/dispense_permission_list.html', context)

@login_required
def dispense_permission_create(request):
    """إنشاء إذن صرف جديد"""
    if request.method == 'POST':
        form = DispensePermissionForm(request.POST)
        formset = DispensePermissionItemFormSet(request.POST)

        if form.is_valid() and formset.is_valid():
            permission = form.save(commit=False)
            permission.created_by = request.user
            permission.save()

            # حفظ عناصر الإذن
            formset.instance = permission
            items = formset.save(commit=False)

            total_value = 0

            for item in items:
                item.save()
                total_value += item.total_cost

                # تسجيل حركة الصنف
                ProductMovement.objects.create(
                    product=item.product,
                    movement_type='dispense',
                    movement_date=permission.dispense_date,
                    quantity=item.quantity,
                    unit_price=item.unit_cost,
                    reference_number=permission.permission_number,
                    reference_type='إذن صرف',
                    warehouse=permission.warehouse,
                    representative=permission.representative
                )

            # تحديث إجمالي الإذن
            permission.total_value = total_value
            permission.save()

            messages.success(request, f'تم إنشاء إذن الصرف {permission.permission_number} بنجاح')
            return redirect('sales:dispense_permission_detail', pk=permission.pk)
    else:
        form = DispensePermissionForm()
        formset = DispensePermissionItemFormSet()

    context = {
        'form': form,
        'formset': formset,
        'title': 'إنشاء إذن صرف جديد'
    }
    return render(request, 'sales/dispense_permission_form.html', context)

@login_required
def dispense_permission_detail(request, pk):
    """تفاصيل إذن الصرف"""
    permission = get_object_or_404(DispensePermission, pk=pk)
    context = {
        'permission': permission,
        'items': permission.items.all(),
    }
    return render(request, 'sales/dispense_permission_detail.html', context)

@login_required
def dispense_permission_edit(request, pk):
    """تعديل إذن الصرف"""
    permission = get_object_or_404(DispensePermission, pk=pk)

    if permission.is_approved:
        messages.error(request, 'لا يمكن تعديل إذن صرف معتمد')
        return redirect('sales:dispense_permission_detail', pk=pk)

    if request.method == 'POST':
        form = DispensePermissionForm(request.POST, instance=permission)
        formset = DispensePermissionItemFormSet(request.POST, instance=permission)

        if form.is_valid() and formset.is_valid():
            form.save()
            formset.save()

            # إعادة حساب الإجمالي
            total_value = sum(item.total_cost for item in permission.items.all())
            permission.total_value = total_value
            permission.save()

            messages.success(request, 'تم تحديث إذن الصرف بنجاح')
            return redirect('sales:dispense_permission_detail', pk=pk)
    else:
        form = DispensePermissionForm(instance=permission)
        formset = DispensePermissionItemFormSet(instance=permission)

    context = {
        'form': form,
        'formset': formset,
        'permission': permission,
        'title': 'تعديل إذن الصرف'
    }
    return render(request, 'sales/dispense_permission_form.html', context)

@login_required
def dispense_permission_approve(request, pk):
    """اعتماد إذن الصرف"""
    permission = get_object_or_404(DispensePermission, pk=pk)

    if permission.is_approved:
        messages.error(request, 'هذا الإذن معتمد مسبقاً')
        return redirect('sales:dispense_permission_detail', pk=pk)

    if request.method == 'POST':
        permission.is_approved = True
        permission.approved_by = request.user
        permission.save()

        # تحديث المخزون
        for item in permission.items.all():
            item.product.stock_quantity -= item.quantity
            item.product.save()

        messages.success(request, 'تم اعتماد إذن الصرف بنجاح وتم تحديث المخزون')
        return redirect('sales:dispense_permission_detail', pk=pk)

    context = {'permission': permission}
    return render(request, 'sales/dispense_permission_approve.html', context)

@login_required
def dispense_permission_print(request, pk):
    """طباعة إذن الصرف"""
    permission = get_object_or_404(DispensePermission, pk=pk)

    context = {
        'permission': permission,
        'items': permission.items.all(),
        'company_name': 'شركة أوساريك',
    }

    return render(request, 'sales/dispense_permission_print.html', context)

# ========== إدارة الجرد ==========

@login_required
def inventory_list(request):
    """قائمة الجرد"""
    search = request.GET.get('search', '')
    inventory_type = request.GET.get('inventory_type', '')
    status = request.GET.get('status', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')

    inventories = Inventory.objects.select_related('vehicle', 'representative__user', 'created_by', 'approved_by')

    if search:
        inventories = inventories.filter(
            Q(inventory_number__icontains=search) |
            Q(warehouse__icontains=search) |
            Q(representative__user__first_name__icontains=search) |
            Q(representative__user__last_name__icontains=search)
        )

    if inventory_type:
        inventories = inventories.filter(inventory_type=inventory_type)

    if status == 'approved':
        inventories = inventories.filter(is_approved=True)
    elif status == 'pending':
        inventories = inventories.filter(is_approved=False)

    if date_from:
        inventories = inventories.filter(inventory_date__gte=date_from)

    if date_to:
        inventories = inventories.filter(inventory_date__lte=date_to)

    inventories = inventories.order_by('-created_at')

    paginator = Paginator(inventories, 20)
    page_number = request.GET.get('page')
    inventories = paginator.get_page(page_number)

    context = {
        'inventories': inventories,
        'search': search,
        'inventory_type': inventory_type,
        'status': status,
        'date_from': date_from,
        'date_to': date_to,
        'type_choices': Inventory._meta.get_field('inventory_type').choices,
    }
    return render(request, 'sales/inventory_list.html', context)

@login_required
def inventory_create(request):
    """إنشاء جرد جديد"""
    if request.method == 'POST':
        form = InventoryForm(request.POST)
        formset = InventoryItemFormSet(request.POST)

        if form.is_valid() and formset.is_valid():
            inventory = form.save(commit=False)
            inventory.created_by = request.user
            inventory.save()

            # حفظ عناصر الجرد
            formset.instance = inventory
            items = formset.save(commit=False)

            total_value = 0

            for item in items:
                # تحديد الكمية النظامية من المخزون الحالي
                if not item.system_quantity:
                    item.system_quantity = item.product.stock_quantity

                item.save()
                total_value += item.total_value

            # تحديث إجمالي الجرد
            inventory.total_value = total_value
            inventory.save()

            messages.success(request, f'تم إنشاء الجرد {inventory.inventory_number} بنجاح')
            return redirect('sales:inventory_detail', pk=inventory.pk)
    else:
        form = InventoryForm()
        formset = InventoryItemFormSet()

    context = {
        'form': form,
        'formset': formset,
        'title': 'إنشاء جرد جديد'
    }
    return render(request, 'sales/inventory_form.html', context)

@login_required
def inventory_detail(request, pk):
    """تفاصيل الجرد"""
    inventory = get_object_or_404(Inventory, pk=pk)

    # حساب الإحصائيات
    items = inventory.items.all()
    total_variance_positive = sum(item.variance_value for item in items if item.variance_value > 0)
    total_variance_negative = sum(item.variance_value for item in items if item.variance_value < 0)
    items_with_variance = items.exclude(variance_quantity=0).count()

    context = {
        'inventory': inventory,
        'items': items,
        'total_variance_positive': total_variance_positive,
        'total_variance_negative': total_variance_negative,
        'items_with_variance': items_with_variance,
    }
    return render(request, 'sales/inventory_detail.html', context)

@login_required
def inventory_edit(request, pk):
    """تعديل الجرد"""
    inventory = get_object_or_404(Inventory, pk=pk)

    if inventory.is_approved:
        messages.error(request, 'لا يمكن تعديل جرد معتمد')
        return redirect('sales:inventory_detail', pk=pk)

    if request.method == 'POST':
        form = InventoryForm(request.POST, instance=inventory)
        formset = InventoryItemFormSet(request.POST, instance=inventory)

        if form.is_valid() and formset.is_valid():
            form.save()
            formset.save()

            # إعادة حساب الإجمالي
            total_value = sum(item.total_value for item in inventory.items.all())
            inventory.total_value = total_value
            inventory.save()

            messages.success(request, 'تم تحديث الجرد بنجاح')
            return redirect('sales:inventory_detail', pk=pk)
    else:
        form = InventoryForm(instance=inventory)
        formset = InventoryItemFormSet(instance=inventory)

    context = {
        'form': form,
        'formset': formset,
        'inventory': inventory,
        'title': 'تعديل الجرد'
    }
    return render(request, 'sales/inventory_form.html', context)

@login_required
def inventory_approve(request, pk):
    """اعتماد الجرد"""
    inventory = get_object_or_404(Inventory, pk=pk)

    if inventory.is_approved:
        messages.error(request, 'هذا الجرد معتمد مسبقاً')
        return redirect('sales:inventory_detail', pk=pk)

    if request.method == 'POST':
        inventory.is_approved = True
        inventory.approved_by = request.user
        inventory.save()

        # تحديث المخزون حسب الفروقات
        for item in inventory.items.all():
            if item.variance_quantity != 0:
                # تسجيل حركة التسوية
                ProductMovement.objects.create(
                    product=item.product,
                    movement_type='adjustment',
                    movement_date=inventory.inventory_date,
                    quantity=abs(item.variance_quantity),
                    unit_price=item.unit_cost,
                    reference_number=inventory.inventory_number,
                    reference_type='تسوية جرد',
                    warehouse=inventory.warehouse,
                    representative=inventory.representative,
                    notes=f'تسوية جرد - الفرق: {item.variance_quantity}'
                )

                # تحديث المخزون
                item.product.stock_quantity = item.actual_quantity
                item.product.save()

        messages.success(request, 'تم اعتماد الجرد بنجاح وتم تحديث المخزون')
        return redirect('sales:inventory_detail', pk=pk)

    context = {'inventory': inventory}
    return render(request, 'sales/inventory_approve.html', context)

@login_required
def inventory_auto_load_products(request, pk):
    """تحميل تلقائي للمنتجات في الجرد"""
    inventory = get_object_or_404(Inventory, pk=pk)

    if inventory.is_approved:
        return JsonResponse({'error': 'الجرد معتمد'}, status=400)

    # مسح العناصر الحالية
    inventory.items.all().delete()

    # إضافة جميع المنتجات النشطة
    products = Product.objects.filter(is_active=True)

    for product in products:
        InventoryItem.objects.create(
            inventory=inventory,
            product=product,
            system_quantity=product.stock_quantity,
            actual_quantity=product.stock_quantity,  # افتراضياً نفس الكمية النظامية
            unit_cost=product.cost_price
        )

    # إعادة حساب الإجمالي
    total_value = sum(item.total_value for item in inventory.items.all())
    inventory.total_value = total_value
    inventory.save()

    return JsonResponse({
        'success': True,
        'message': f'تم تحميل {products.count()} منتج بنجاح',
        'total_products': products.count(),
        'total_value': float(total_value)
    })

# ========== نظام التقارير والتحليلات ==========

@login_required
def reports_dashboard(request):
    """لوحة تحكم التقارير"""
    today = datetime.now().date()

    # إحصائيات سريعة
    context = {
        'today': today,
        'total_sales_today': SalesInvoice.objects.filter(
            invoice_date=today, status='paid'
        ).aggregate(total=Sum('total_amount'))['total'] or 0,

        'total_orders_today': SalesOrder.objects.filter(order_date=today).count(),

        'total_returns_today': SalesReturn.objects.filter(
            return_date=today, is_approved=True
        ).aggregate(total=Sum('total_amount'))['total'] or 0,

        'active_representatives': SalesRepresentative.objects.filter(is_active=True).count(),
    }

    return render(request, 'sales/reports_dashboard.html', context)

@login_required
def sales_report(request):
    """تقرير المبيعات"""
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    representative = request.GET.get('representative', '')
    customer_type = request.GET.get('customer_type', '')
    invoice_type = request.GET.get('invoice_type', '')

    # تحديد الفترة الافتراضية (آخر 30 يوم)
    if not date_from:
        date_from = (datetime.now() - timedelta(days=30)).date()
    else:
        date_from = datetime.strptime(date_from, '%Y-%m-%d').date()

    if not date_to:
        date_to = datetime.now().date()
    else:
        date_to = datetime.strptime(date_to, '%Y-%m-%d').date()

    # استعلام الفواتير
    invoices = SalesInvoice.objects.filter(
        invoice_date__range=[date_from, date_to]
    ).select_related('customer', 'representative__user')

    if representative:
        invoices = invoices.filter(representative_id=representative)

    if customer_type:
        invoices = invoices.filter(customer__customer_type=customer_type)

    if invoice_type:
        invoices = invoices.filter(invoice_type=invoice_type)

    # حساب الإحصائيات
    total_invoices = invoices.count()
    total_amount = invoices.aggregate(total=Sum('total_amount'))['total'] or 0
    paid_amount = invoices.filter(status='paid').aggregate(total=Sum('total_amount'))['total'] or 0
    pending_amount = invoices.filter(status='sent').aggregate(total=Sum('total_amount'))['total'] or 0

    # تجميع حسب المندوب
    rep_stats = invoices.values(
        'representative__user__first_name',
        'representative__user__last_name'
    ).annotate(
        total_sales=Sum('total_amount'),
        invoice_count=Count('id')
    ).order_by('-total_sales')

    # تجميع حسب نوع العميل
    customer_type_stats = invoices.values('customer__customer_type').annotate(
        total_sales=Sum('total_amount'),
        invoice_count=Count('id')
    ).order_by('-total_sales')

    # تجميع حسب نوع الفاتورة
    invoice_type_stats = invoices.values('invoice_type').annotate(
        total_sales=Sum('total_amount'),
        invoice_count=Count('id')
    ).order_by('-total_sales')

    context = {
        'invoices': invoices[:100],  # أول 100 فاتورة للعرض
        'date_from': date_from,
        'date_to': date_to,
        'representative': representative,
        'customer_type': customer_type,
        'invoice_type': invoice_type,
        'representatives': SalesRepresentative.objects.filter(is_active=True),
        'customer_types': Customer.CUSTOMER_TYPES,
        'invoice_types': SalesInvoice.INVOICE_TYPES,
        'total_invoices': total_invoices,
        'total_amount': total_amount,
        'paid_amount': paid_amount,
        'pending_amount': pending_amount,
        'rep_stats': rep_stats,
        'customer_type_stats': customer_type_stats,
        'invoice_type_stats': invoice_type_stats,
    }

    return render(request, 'sales/sales_report.html', context)

@login_required
def inventory_report(request):
    """تقرير المخزون"""
    search = request.GET.get('search', '')
    category = request.GET.get('category', '')
    report_type = request.GET.get('report_type', 'all')

    products = Product.objects.filter(is_active=True)

    if search:
        products = products.filter(
            Q(name__icontains=search) |
            Q(code__icontains=search)
        )

    if category:
        products = products.filter(category__icontains=category)

    # تطبيق فلتر نوع التقرير
    if report_type == 'low_stock':
        products = products.filter(
            stock_quantity__lte=F('min_stock_level'),
            min_stock_level__isnull=False
        )
    elif report_type == 'out_of_stock':
        products = products.filter(stock_quantity=0)
    elif report_type == 'overstocked':
        products = products.filter(
            stock_quantity__gte=F('max_stock_level'),
            max_stock_level__isnull=False
        )

    # حساب الإحصائيات
    all_products = Product.objects.filter(is_active=True)
    total_products = all_products.count()

    # حساب القيمة الإجمالية للمخزون بأمان
    total_inventory_value = 0
    for product in all_products:
        if product.cost_price and product.stock_quantity:
            total_inventory_value += product.stock_quantity * product.cost_price

    # حساب المنتجات ذات المخزون المنخفض
    low_stock_products = all_products.filter(
        stock_quantity__lte=F('min_stock_level'),
        min_stock_level__isnull=False
    ).count()

    # حساب المنتجات النافدة
    out_of_stock_products = all_products.filter(stock_quantity=0).count()

    # الحصول على الفئات المتاحة
    categories = Product.objects.values_list('category', flat=True).distinct().exclude(category__isnull=True)

    # ترقيم الصفحات
    paginator = Paginator(products, 20)
    page_number = request.GET.get('page')
    products = paginator.get_page(page_number)

    # إنشاء تنبيهات المخزون
    alerts = []
    for product in all_products:
        if product.stock_quantity == 0:
            alerts.append({
                'type': 'critical',
                'product': product,
                'message': f'نفد مخزون {product.name} تماماً'
            })
        elif product.min_stock_level and product.stock_quantity <= product.min_stock_level:
            alerts.append({
                'type': 'warning',
                'product': product,
                'message': f'مخزون {product.name} أقل من الحد الأدنى ({product.min_stock_level})'
            })

    # أعلى المنتجات قيمة
    top_products_names = []
    top_products_values = []
    top_products = all_products.filter(cost_price__isnull=False).order_by('-cost_price')[:5]
    for product in top_products:
        top_products_names.append(product.name)
        inventory_value = (product.stock_quantity or 0) * (product.cost_price or 0)
        top_products_values.append(float(inventory_value))

    context = {
        'products': products,
        'search': search,
        'selected_category': category,
        'report_type': report_type,
        'categories': categories,
        'total_products': total_products,
        'total_inventory_value': total_inventory_value,
        'low_stock_products': low_stock_products,
        'out_of_stock_products': out_of_stock_products,
        'alerts': alerts[:10],  # أول 10 تنبيهات فقط
        'top_products_names': json.dumps(top_products_names),
        'top_products_values': json.dumps(top_products_values),
    }

    return render(request, 'sales/inventory_report.html', context)

@login_required
def representative_performance_report(request):
    """تقرير أداء المناديب"""
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    representative = request.GET.get('representative', '')

    # تحديد الفترة الافتراضية (الشهر الحالي)
    if not date_from:
        date_from = datetime.now().replace(day=1).date()
    else:
        date_from = datetime.strptime(date_from, '%Y-%m-%d').date()

    if not date_to:
        date_to = datetime.now().date()
    else:
        date_to = datetime.strptime(date_to, '%Y-%m-%d').date()

    # استعلام المناديب
    representatives = SalesRepresentative.objects.filter(is_active=True)

    if representative:
        representatives = representatives.filter(id=representative)

    # حساب الأداء لكل مندوب
    performance_data = []

    for rep in representatives:
        # المبيعات
        sales = SalesInvoice.objects.filter(
            representative=rep,
            invoice_date__range=[date_from, date_to],
            status='paid'
        )

        total_sales = sales.aggregate(total=Sum('total_amount'))['total'] or 0
        sales_count = sales.count()

        # التحصيلات
        collections = Payment.objects.filter(
            representative=rep,
            payment_date__range=[date_from, date_to],
            payment_type='collection'
        )

        total_collections = collections.aggregate(total=Sum('amount'))['total'] or 0
        collections_count = collections.count()

        # المرتجعات
        returns = SalesReturn.objects.filter(
            representative=rep,
            return_date__range=[date_from, date_to],
            is_approved=True
        )

        total_returns = returns.aggregate(total=Sum('total_amount'))['total'] or 0
        returns_count = returns.count()

        # حساب النسب
        target_achievement = (total_sales / rep.target_monthly * 100) if rep.target_monthly > 0 else 0
        commission = total_sales * (rep.commission_rate / 100)

        performance_data.append({
            'representative': rep,
            'total_sales': total_sales,
            'sales_count': sales_count,
            'total_collections': total_collections,
            'collections_count': collections_count,
            'total_returns': total_returns,
            'returns_count': returns_count,
            'target_achievement': target_achievement,
            'commission': commission,
            'net_sales': total_sales - total_returns,
        })

    # ترتيب حسب المبيعات
    performance_data.sort(key=lambda x: x['total_sales'], reverse=True)

    context = {
        'performance_data': performance_data,
        'date_from': date_from,
        'date_to': date_to,
        'representative': representative,
        'representatives': SalesRepresentative.objects.filter(is_active=True),
    }

    return render(request, 'sales/representative_performance_report.html', context)

# ========== API للحصول على بيانات المنتجات ==========
@login_required
def get_product_data(request, product_id):
    """API للحصول على بيانات المنتج"""
    try:
        product = Product.objects.get(id=product_id)
        data = {
            'unit_price_wholesale': float(product.unit_price_wholesale),
            'unit_price_retail': float(product.unit_price_retail),
            'stock_quantity': float(product.stock_quantity),
            'unit': product.unit,
        }
        return JsonResponse(data)
    except Product.DoesNotExist:
        return JsonResponse({'error': 'المنتج غير موجود'}, status=404)
