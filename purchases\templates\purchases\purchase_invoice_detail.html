{% extends 'base.html' %}

{% block title %}تفاصيل فاتورة الشراء {{ invoice.invoice_number }} - نظام أوساريك{% endblock %}

{% block extra_css %}
    <style>
        .page-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 40px 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .invoice-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 25px;
            color: #333;
            border-bottom: 3px solid #dc3545;
            padding-bottom: 10px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #495057;
            min-width: 150px;
        }
        
        .info-value {
            color: #333;
            flex: 1;
            text-align: left;
        }
        
        .status-badge {
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.85rem;
        }
        
        .status-received {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        
        .status-paid {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
        }
        
        .status-overdue {
            background: linear-gradient(45deg, #dc3545, #c82333);
            color: white;
        }
        
        .status-cancelled {
            background: linear-gradient(45deg, #6c757d, #5a6268);
            color: white;
        }
        
        .items-table {
            background: white;
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .table {
            border-radius: 15px;
            overflow: hidden;
        }
        
        .table thead th {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border: none;
            padding: 15px;
            font-weight: 600;
        }
        
        .table tbody td {
            padding: 15px;
            vertical-align: middle;
            border-bottom: 1px solid #e9ecef;
        }
        
        .total-section {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }
        
        .total-row:last-child {
            margin-bottom: 0;
            font-size: 1.3rem;
            font-weight: 700;
            border-top: 2px solid rgba(255, 255, 255, 0.3);
            padding-top: 20px;
            margin-top: 20px;
        }
        
        .action-buttons {
            background: white;
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .btn-action {
            padding: 12px 25px;
            border-radius: 25px;
            border: none;
            margin: 0 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-action:hover {
            transform: translateY(-2px);
            text-decoration: none;
        }
        
        .btn-edit {
            background: linear-gradient(45deg, #ffc107, #e0a800);
            color: white;
        }
        
        .btn-edit:hover {
            color: white;
            box-shadow: 0 6px 12px rgba(255, 193, 7, 0.3);
        }
        
        .btn-print {
            background: linear-gradient(45deg, #6f42c1, #5a32a3);
            color: white;
        }
        
        .btn-print:hover {
            color: white;
            box-shadow: 0 6px 12px rgba(111, 66, 193, 0.3);
        }
        
        .btn-delete {
            background: linear-gradient(45deg, #dc3545, #c82333);
            color: white;
        }
        
        .btn-delete:hover {
            color: white;
            box-shadow: 0 6px 12px rgba(220, 53, 69, 0.3);
        }
        
        .btn-back {
            background: linear-gradient(45deg, #6c757d, #5a6268);
            color: white;
        }
        
        .btn-back:hover {
            color: white;
            box-shadow: 0 6px 12px rgba(108, 117, 125, 0.3);
        }
        
        .supplier-info {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .supplier-name {
            font-size: 1.2rem;
            font-weight: 700;
            color: #dc3545;
            margin-bottom: 10px;
        }
        
        .supplier-details {
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="container-fluid py-4">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="bi bi-receipt"></i>
                        فاتورة الشراء {{ invoice.invoice_number }}
                    </h1>
                    <p class="mb-0">تفاصيل فاتورة الشراء من {{ invoice.supplier.name }}</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{% url 'purchases:purchase_invoice_list' %}" class="btn btn-outline-light">
                        <i class="bi bi-arrow-left"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>

        <!-- عرض الرسائل -->
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}

        <div class="row">
            <!-- معلومات الفاتورة -->
            <div class="col-md-8">
                <div class="invoice-section">
                    <h3 class="section-title">معلومات الفاتورة</h3>
                    
                    <div class="info-row">
                        <span class="info-label">رقم الفاتورة:</span>
                        <span class="info-value"><strong>{{ invoice.invoice_number }}</strong></span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">تاريخ الفاتورة:</span>
                        <span class="info-value">{{ invoice.invoice_date|date:"d/m/Y" }}</span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">تاريخ الاستحقاق:</span>
                        <span class="info-value">
                            {% if invoice.due_date %}
                                {{ invoice.due_date|date:"d/m/Y" }}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">الحالة:</span>
                        <span class="info-value">
                            {% if invoice.status == 'received' %}
                                <span class="status-badge status-received">مستلمة</span>
                            {% elif invoice.status == 'paid' %}
                                <span class="status-badge status-paid">مدفوعة</span>
                            {% elif invoice.status == 'overdue' %}
                                <span class="status-badge status-overdue">متأخرة</span>
                            {% elif invoice.status == 'cancelled' %}
                                <span class="status-badge status-cancelled">ملغية</span>
                            {% endif %}
                        </span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">تاريخ الإنشاء:</span>
                        <span class="info-value">{{ invoice.created_at|date:"d/m/Y H:i" }}</span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">آخر تحديث:</span>
                        <span class="info-value">{{ invoice.updated_at|date:"d/m/Y H:i" }}</span>
                    </div>
                    
                    {% if invoice.notes %}
                        <div class="info-row">
                            <span class="info-label">ملاحظات:</span>
                            <span class="info-value">{{ invoice.notes }}</span>
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- معلومات المورد -->
            <div class="col-md-4">
                <div class="invoice-section">
                    <h3 class="section-title">معلومات المورد</h3>
                    
                    <div class="supplier-info">
                        <div class="supplier-name">{{ invoice.supplier.name }}</div>
                        <div class="supplier-details">
                            {% if invoice.supplier.phone %}
                                <div><i class="bi bi-telephone"></i> {{ invoice.supplier.phone }}</div>
                            {% endif %}
                            {% if invoice.supplier.email %}
                                <div><i class="bi bi-envelope"></i> {{ invoice.supplier.email }}</div>
                            {% endif %}
                            {% if invoice.supplier.address %}
                                <div><i class="bi bi-geo-alt"></i> {{ invoice.supplier.address }}</div>
                            {% endif %}
                            {% if invoice.supplier.payment_terms %}
                                <div><i class="bi bi-credit-card"></i> {{ invoice.supplier.payment_terms }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أصناف الفاتورة -->
        <div class="items-table">
            <h3 class="section-title">أصناف الفاتورة</h3>
            
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>المنتج</th>
                            <th>المخزن</th>
                            <th>الكمية</th>
                            <th>سعر الوحدة</th>
                            <th>الخصم</th>
                            <th>الإجمالي</th>
                            <th>حالة المخزون</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in invoice.items.all %}
                            <tr>
                                <td>
                                    <div>
                                        <strong>{{ item.product.name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ item.product.code }}</small>
                                    </div>
                                </td>
                                <td>
                                    {% if item.warehouse %}
                                        <strong>{{ item.warehouse.name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ item.warehouse.code }}</small>
                                    {% else %}
                                        <span class="text-warning">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>{{ item.quantity }} {{ item.product.unit }}</td>
                                <td>{{ item.unit_price|floatformat:2 }} ج.م</td>
                                <td>{{ item.discount_percentage|floatformat:1 }}%</td>
                                <td><strong>{{ item.total_price|floatformat:2 }} ج.م</strong></td>
                                <td>
                                    {% if item.applied_to_stock %}
                                        <span class="status-badge status-paid">
                                            <i class="bi bi-check-circle"></i>
                                            تم التطبيق
                                        </span>
                                        <br>
                                        <small class="text-muted">{{ item.applied_at|date:"d/m/Y H:i" }}</small>
                                    {% else %}
                                        <span class="status-badge status-overdue">
                                            <i class="bi bi-clock"></i>
                                            في الانتظار
                                        </span>
                                    {% endif %}
                                </td>
                            </tr>
                        {% empty %}
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="bi bi-box" style="font-size: 2rem;"></i>
                                        <p class="mt-2">لا توجد أصناف في هذه الفاتورة</p>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- إجمالي الفاتورة -->
            <div class="total-section">
                <div class="total-row">
                    <span>المجموع الفرعي:</span>
                    <span>{{ invoice.subtotal|floatformat:2 }} ج.م</span>
                </div>
                <div class="total-row">
                    <span>إجمالي الخصم:</span>
                    <span>{{ invoice.total_discount|floatformat:2 }} ج.م</span>
                </div>
                <div class="total-row">
                    <span>الضريبة ({{ invoice.tax_percentage|floatformat:1 }}%):</span>
                    <span>{{ invoice.tax_amount|floatformat:2 }} ج.م</span>
                </div>
                <div class="total-row">
                    <span>الإجمالي النهائي:</span>
                    <span>{{ invoice.total_amount|floatformat:2 }} ج.م</span>
                </div>
            </div>
        </div>

        <!-- أزرار الإجراءات -->
        <div class="action-buttons">
            {% if not all_items_applied %}
                <button type="button" class="btn-action" style="background: linear-gradient(45deg, #28a745, #20c997); color: white;"
                        onclick="applyToStock('{{ invoice.pk }}')">
                    <i class="bi bi-box-arrow-in-down"></i>
                    تطبيق على المخزون
                </button>
            {% endif %}

            <a href="{% url 'purchases:purchase_invoice_edit' invoice.pk %}" class="btn-action btn-edit">
                <i class="bi bi-pencil"></i>
                تعديل الفاتورة
            </a>

            <a href="{% url 'purchases:purchase_invoice_print' invoice.pk %}" class="btn-action btn-print">
                <i class="bi bi-printer"></i>
                طباعة الفاتورة
            </a>

            <button type="button" class="btn-action btn-delete"
                    onclick="confirmDelete('{{ invoice.invoice_number }}', '{% url 'purchases:purchase_invoice_delete' invoice.pk %}')">
                <i class="bi bi-trash"></i>
                حذف الفاتورة
            </button>

            <a href="{% url 'purchases:dashboard' %}" class="btn-action btn-back">
                <i class="bi bi-house"></i>
                لوحة التحكم
            </a>

            <a href="{% url 'purchases:purchase_invoice_list' %}" class="btn-action btn-back">
                <i class="bi bi-arrow-left"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
    <script>
        function confirmDelete(invoiceNumber, deleteUrl) {
            if (confirm(`هل أنت متأكد من حذف فاتورة الشراء "${invoiceNumber}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
                window.location.href = deleteUrl;
            }
        }

        function applyToStock(invoiceId) {
            if (confirm('هل أنت متأكد من تطبيق هذه الفاتورة على المخزون؟\n\nسيتم إضافة الكميات إلى المخازن المحددة وتسجيل حركات المخزون.\n\nهذا الإجراء لا يمكن التراجع عنه.')) {
                // إظهار مؤشر التحميل
                const button = event.target;
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="bi bi-hourglass-split"></i> جاري التطبيق...';
                button.disabled = true;

                // إرسال طلب AJAX
                fetch(`/purchases/purchase-invoices/${invoiceId}/apply-to-stock/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('تم تطبيق الفاتورة على المخزون بنجاح!');
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + data.message);
                        button.innerHTML = originalText;
                        button.disabled = false;
                    }
                })
                .catch(error => {
                    alert('حدث خطأ في الاتصال');
                    button.innerHTML = originalText;
                    button.disabled = false;
                });
            }
        }
    </script>

    <!-- إضافة CSRF token للـ AJAX -->
    {% csrf_token %}
{% endblock %}
