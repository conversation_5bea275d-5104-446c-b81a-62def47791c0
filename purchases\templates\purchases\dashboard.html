{% extends 'base.html' %}

{% block title %}لوحة تحكم المشتريات - نظام أوساريك{% endblock %}

{% block extra_css %}
    <style>
        
        .page-header {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: white;
            padding: 40px 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border-left: 5px solid;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            border-radius: 50%;
            transform: translate(20px, -20px);
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }
        
        .stat-card.suppliers { border-left-color: #28a745; }
        .stat-card.orders { border-left-color: #ffc107; }
        .stat-card.invoices { border-left-color: #17a2b8; }
        .stat-card.amount { border-left-color: #6f42c1; }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            margin-bottom: 15px;
            color: white;
        }
        
        .icon-suppliers { background: linear-gradient(45deg, #28a745, #20c997); }
        .icon-orders { background: linear-gradient(45deg, #ffc107, #e0a800); }
        .icon-invoices { background: linear-gradient(45deg, #17a2b8, #138496); }
        .icon-amount { background: linear-gradient(45deg, #6f42c1, #5a32a3); }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 5px;
            color: #333;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 1rem;
            font-weight: 500;
        }
        
        .quick-actions {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 25px;
            color: #333;
            text-align: center;
            border-bottom: 3px solid #ffc107;
            padding-bottom: 15px;
        }
        
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .action-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            text-decoration: none;
            color: inherit;
        }
        
        .action-card:hover {
            border-color: #ffc107;
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(255, 193, 7, 0.2);
            color: inherit;
            text-decoration: none;
        }
        
        .action-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #ffc107;
        }
        
        .action-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
        }
        
        .action-description {
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        .recent-activity {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .activity-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            background: #f8f9fa;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .activity-item:hover {
            background: #e9ecef;
            transform: translateX(-5px);
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-left: 15px;
            font-size: 1.2rem;
        }
        
        .activity-content h6 {
            margin: 0;
            font-weight: 600;
            color: #333;
        }
        
        .activity-content small {
            color: #6c757d;
        }
        
        .alerts-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .alert-item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 10px;
            border-left: 4px solid;
        }
        
        .alert-warning {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        
        .alert-danger {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        
        .alert-info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
        }
        
        .alert-icon {
            font-size: 1.5rem;
            margin-left: 15px;
        }
        
        .alert-content h6 {
            margin: 0;
            font-weight: 600;
        }
        
        .alert-content p {
            margin: 0;
            font-size: 0.9rem;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="container-fluid py-4">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="bi bi-cart-plus"></i>
                        لوحة تحكم المشتريات
                    </h1>
                    <p class="mb-0">إدارة شاملة لجميع عمليات الشراء والموردين</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex gap-2 justify-content-end">
                        <a href="{% url 'purchases:supplier_list' %}" class="btn btn-outline-light">
                            <i class="bi bi-people"></i>
                            الموردين
                        </a>
                        <a href="{% url 'purchases:reports' %}" class="btn btn-outline-light">
                            <i class="bi bi-graph-up"></i>
                            التقارير
                        </a>
                        <a href="{% url 'purchases:purchase_order_create' %}" class="btn btn-outline-light">
                            <i class="bi bi-plus-circle"></i>
                            أمر شراء جديد
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإحصائيات الرئيسية -->
        <div class="stats-grid">
            <div class="stat-card suppliers">
                <div class="stat-icon icon-suppliers">
                    <i class="bi bi-people"></i>
                </div>
                <div class="stat-number">{{ total_suppliers|default:0 }}</div>
                <div class="stat-label">إجمالي الموردين</div>
            </div>

            <div class="stat-card orders">
                <div class="stat-icon icon-orders">
                    <i class="bi bi-cart-plus"></i>
                </div>
                <div class="stat-number">{{ total_orders|default:0 }}</div>
                <div class="stat-label">أوامر الشراء</div>
            </div>

            <div class="stat-card invoices">
                <div class="stat-icon icon-invoices">
                    <i class="bi bi-receipt"></i>
                </div>
                <div class="stat-number">{{ total_invoices|default:0 }}</div>
                <div class="stat-label">فواتير الشراء</div>
            </div>

            <div class="stat-card amount">
                <div class="stat-icon icon-amount">
                    <i class="bi bi-currency-exchange"></i>
                </div>
                <div class="stat-number">{{ total_purchases|floatformat:0|default:0 }}</div>
                <div class="stat-label">إجمالي المشتريات (ج.م)</div>
            </div>
        </div>

        <!-- الإجراءات السريعة -->
        <div class="quick-actions">
            <h3 class="section-title">الإجراءات السريعة</h3>
            <div class="actions-grid">
                <a href="{% url 'purchases:purchase_order_create' %}" class="action-card">
                    <div class="action-icon">
                        <i class="bi bi-plus-circle"></i>
                    </div>
                    <div class="action-title">أمر شراء جديد</div>
                    <div class="action-description">إنشاء أمر شراء جديد</div>
                </a>

                <a href="{% url 'purchases:supplier_create' %}" class="action-card">
                    <div class="action-icon">
                        <i class="bi bi-person-plus"></i>
                    </div>
                    <div class="action-title">مورد جديد</div>
                    <div class="action-description">إضافة مورد جديد للنظام</div>
                </a>

                <a href="{% url 'purchases:supplier_list' %}" class="action-card">
                    <div class="action-icon">
                        <i class="bi bi-people"></i>
                    </div>
                    <div class="action-title">إدارة الموردين</div>
                    <div class="action-description">عرض وإدارة جميع الموردين</div>
                </a>

                <a href="{% url 'purchases:purchase_invoice_create' %}" class="action-card">
                    <div class="action-icon">
                        <i class="bi bi-receipt"></i>
                    </div>
                    <div class="action-title">فاتورة شراء</div>
                    <div class="action-description">إنشاء فاتورة شراء جديدة</div>
                </a>

                <a href="{% url 'purchases:reports' %}" class="action-card">
                    <div class="action-icon">
                        <i class="bi bi-graph-up"></i>
                    </div>
                    <div class="action-title">التقارير</div>
                    <div class="action-description">عرض تقارير المشتريات</div>
                </a>

                <a href="{% url 'purchases:purchase_order_list' %}" class="action-card">
                    <div class="action-icon">
                        <i class="bi bi-list-check"></i>
                    </div>
                    <div class="action-title">أوامر الشراء</div>
                    <div class="action-description">عرض جميع أوامر الشراء</div>
                </a>

                <a href="{% url 'purchases:purchase_invoice_list' %}" class="action-card">
                    <div class="action-icon">
                        <i class="bi bi-receipt-cutoff"></i>
                    </div>
                    <div class="action-title">فواتير الشراء</div>
                    <div class="action-description">عرض جميع فواتير الشراء</div>
                </a>

                <a href="{% url 'purchases:alerts_dashboard' %}" class="action-card">
                    <div class="action-icon">
                        <i class="bi bi-bell-fill"></i>
                    </div>
                    <div class="action-title">التنبيهات</div>
                    <div class="action-description">مراقبة التنبيهات والإشعارات</div>
                </a>

                <a href="{% url 'purchases:payment_approvals' %}" class="action-card">
                    <div class="action-icon">
                        <i class="bi bi-clipboard-check"></i>
                    </div>
                    <div class="action-title">الموافقات</div>
                    <div class="action-description">إدارة موافقات المدفوعات</div>
                </a>

                <a href="{% url 'purchases:cash_flow_forecast' %}" class="action-card">
                    <div class="action-icon">
                        <i class="bi bi-graph-up-arrow"></i>
                    </div>
                    <div class="action-title">توقعات التدفق النقدي</div>
                    <div class="action-description">تحليل التدفقات النقدية</div>
                </a>

                <a href="{% url 'purchases:suppliers_financial_summary' %}" class="action-card">
                    <div class="action-icon">
                        <i class="bi bi-people-fill"></i>
                    </div>
                    <div class="action-title">الملخص المالي للموردين</div>
                    <div class="action-description">تقرير الديون والمدفوعات</div>
                </a>
            </div>
        </div>

        <!-- التنبيهات والتحذيرات -->
        {% if pending_orders > 0 or low_stock_products > 0 or overdue_invoices > 0 %}
        <div class="alerts-section">
            <h3 class="section-title">التنبيهات والتحذيرات</h3>
            
            {% if pending_orders > 0 %}
                <div class="alert-item alert-warning">
                    <div class="alert-icon">
                        <i class="bi bi-exclamation-triangle"></i>
                    </div>
                    <div class="alert-content">
                        <h6>أوامر شراء معلقة</h6>
                        <p>يوجد {{ pending_orders }} أمر شراء في انتظار المعالجة</p>
                    </div>
                </div>
            {% endif %}

            {% if low_stock_products > 0 %}
                <div class="alert-item alert-danger">
                    <div class="alert-icon">
                        <i class="bi bi-box"></i>
                    </div>
                    <div class="alert-content">
                        <h6>مخزون منخفض</h6>
                        <p>{{ low_stock_products }} منتج تحت الحد الأدنى للمخزون</p>
                    </div>
                </div>
            {% endif %}

            {% if overdue_invoices > 0 %}
                <div class="alert-item alert-info">
                    <div class="alert-icon">
                        <i class="bi bi-calendar-x"></i>
                    </div>
                    <div class="alert-content">
                        <h6>فواتير متأخرة</h6>
                        <p>{{ overdue_invoices }} فاتورة متأخرة الدفع</p>
                    </div>
                </div>
            {% endif %}
        </div>
        {% endif %}

        <!-- النشاط الأخير -->
        <div class="recent-activity">
            <div class="activity-section">
                <h3 class="section-title">أحدث أوامر الشراء</h3>
                {% for order in recent_orders %}
                    <div class="activity-item">
                        <div class="activity-icon" style="background: #ffc107;">
                            <i class="bi bi-cart-plus"></i>
                        </div>
                        <div class="activity-content">
                            <h6>{{ order.order_number }}</h6>
                            <small>{{ order.supplier.name }} - {{ order.order_date|date:"Y/m/d" }}</small>
                        </div>
                    </div>
                {% empty %}
                    <p class="text-muted text-center">لا توجد أوامر شراء حديثة</p>
                {% endfor %}
            </div>

            <div class="activity-section">
                <h3 class="section-title">أحدث فواتير الشراء</h3>
                {% for invoice in recent_invoices %}
                    <div class="activity-item">
                        <div class="activity-icon" style="background: #17a2b8;">
                            <i class="bi bi-receipt"></i>
                        </div>
                        <div class="activity-content">
                            <h6>{{ invoice.invoice_number }}</h6>
                            <small>{{ invoice.supplier.name }} - {{ invoice.invoice_date|date:"Y/m/d" }}</small>
                        </div>
                    </div>
                {% empty %}
                    <p class="text-muted text-center">لا توجد فواتير شراء حديثة</p>
                {% endfor %}
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
    <script>
        // تأثيرات بصرية للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.stat-card, .quick-actions, .alerts-section, .activity-section');
            elements.forEach((element, index) => {
                setTimeout(() => {
                    element.style.opacity = '0';
                    element.style.transform = 'translateY(30px)';
                    element.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        element.style.opacity = '1';
                        element.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
        });
    </script>
{% endblock %}
