{% load static %}
<!DOCTYPE html>
<html lang="{% if user_settings and user_settings.language == 'en' %}en{% else %}ar{% endif %}" dir="{% if user_settings and user_settings.language == 'en' %}ltr{% else %}rtl{% endif %}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{% if user_language == 'en' %}Osaric - Accounting & Inventory Management System{% else %}أوساريك - نظام إدارة الحسابات والمخزون{% endif %}{% endblock %}</title>
    
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.umd.min.js"></script>
    
    <style>
        :root {
            --primary-color: #0d6efd;
            --secondary-color: #0dcaf0;
            --success-color: #198754;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #0dcaf0;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --sidebar-width: 280px;
        }

        /* إعدادات المظهر الديناميكية */
        {% if user_settings %}
            /* حجم الخط */
            {% if user_settings.font_size == 'small' %}
                * { font-size: 12px !important; }
                .sidebar-menu a { font-size: 13px !important; }
                .card-title { font-size: 16px !important; }
                h1 { font-size: 24px !important; }
                h2 { font-size: 20px !important; }
                h3 { font-size: 18px !important; }
            {% elif user_settings.font_size == 'large' %}
                * { font-size: 18px !important; }
                .sidebar-menu a { font-size: 19px !important; }
                .card-title { font-size: 22px !important; }
                h1 { font-size: 32px !important; }
                h2 { font-size: 28px !important; }
                h3 { font-size: 24px !important; }
            {% else %}
                * { font-size: 14px !important; }
                .sidebar-menu a { font-size: 15px !important; }
                .card-title { font-size: 18px !important; }
            {% endif %}
        {% endif %}

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Modern Dropdown Menu Styles */
        .main-menu-dropdown {
            position: fixed;
            top: 80px;
            right: 20px;
            width: 400px;
            max-height: 80vh;
            background: linear-gradient(145deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            box-shadow: 0 25px 60px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            opacity: 0;
            visibility: hidden;
            transform: translateY(-20px) scale(0.95);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 10000;
            overflow: hidden;
        }

        .main-menu-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0) scale(1);
        }

        .main-menu-dropdown::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg,
                rgba(255, 255, 255, 0.1) 0%,
                transparent 50%,
                rgba(255, 255, 255, 0.05) 100%);
            pointer-events: none;
        }

        .main-menu-dropdown::after {
            content: '';
            position: absolute;
            top: -10px;
            right: 30px;
            width: 20px;
            height: 20px;
            background: linear-gradient(145deg, #667eea 0%, #764ba2 100%);
            transform: rotate(45deg);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            border-left: 1px solid rgba(255, 255, 255, 0.2);
        }

        .menu-dropdown-header {
            padding: 1.5rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.15);
            position: relative;
            overflow: hidden;
        }

        .menu-dropdown-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .menu-dropdown-header h3 {
            font-weight: 700;
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            color: white;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .menu-dropdown-header p {
            font-size: 0.85rem;
            opacity: 0.9;
            margin: 0;
            color: rgba(255, 255, 255, 0.9);
        }

        .menu-dropdown-content {
            padding: 1rem 0;
            max-height: 60vh;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
        }

        .menu-dropdown-content::-webkit-scrollbar {
            width: 6px;
        }

        .menu-dropdown-content::-webkit-scrollbar-track {
            background: transparent;
        }

        .menu-dropdown-content::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .menu-dropdown-content::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .menu-nav-item {
            margin: 0.25rem 1rem;
            transform: translateX(30px);
            opacity: 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .main-menu-dropdown.show .menu-nav-item {
            transform: translateX(0);
            opacity: 1;
        }

        .menu-nav-link {
            display: flex;
            align-items: center;
            padding: 0.875rem 1.25rem;
            color: rgba(255,255,255,0.95);
            text-decoration: none;
            border-radius: 12px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            position: relative;
            overflow: hidden;
            font-size: 0.95rem;
        }

        .menu-nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
            transition: left 0.5s ease;
        }

        .menu-nav-link:hover::before {
            left: 100%;
        }

        .menu-nav-link:hover, .menu-nav-link.active {
            background: rgba(255,255,255,0.2);
            color: white;
            transform: translateX(8px) scale(1.02);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
            border-left: 4px solid rgba(255, 255, 255, 0.9);
        }

        .menu-nav-link:active {
            transform: translateX(4px) scale(0.98);
        }

        .menu-nav-link i {
            margin-left: 1rem;
            font-size: 1.2rem;
            width: 24px;
            text-align: center;
            transition: all 0.3s ease;
            color: rgba(255, 255, 255, 0.9);
        }

        .menu-nav-link:hover i {
            transform: scale(1.3) rotate(10deg);
            color: white;
        }

        .menu-nav-link span {
            font-weight: 600;
        }

        /* Staggered animation delays for menu items */
        .main-menu-dropdown.show .menu-nav-item:nth-child(1) { transition-delay: 0.05s; }
        .main-menu-dropdown.show .menu-nav-item:nth-child(2) { transition-delay: 0.1s; }
        .main-menu-dropdown.show .menu-nav-item:nth-child(3) { transition-delay: 0.15s; }
        .main-menu-dropdown.show .menu-nav-item:nth-child(4) { transition-delay: 0.2s; }
        .main-menu-dropdown.show .menu-nav-item:nth-child(5) { transition-delay: 0.25s; }
        .main-menu-dropdown.show .menu-nav-item:nth-child(6) { transition-delay: 0.3s; }
        .main-menu-dropdown.show .menu-nav-item:nth-child(7) { transition-delay: 0.35s; }
        .main-menu-dropdown.show .menu-nav-item:nth-child(8) { transition-delay: 0.4s; }
        .main-menu-dropdown.show .menu-nav-item:nth-child(9) { transition-delay: 0.45s; }
        .main-menu-dropdown.show .menu-nav-item:nth-child(10) { transition-delay: 0.5s; }
        .main-menu-dropdown.show .menu-nav-item:nth-child(11) { transition-delay: 0.55s; }
        .main-menu-dropdown.show .menu-nav-item:nth-child(12) { transition-delay: 0.6s; }

        /* Staggered animation delays for nav items */
        .sidebar.show .nav-item:nth-child(1) .nav-link { transition-delay: 0.1s; }
        .sidebar.show .nav-item:nth-child(2) .nav-link { transition-delay: 0.15s; }
        .sidebar.show .nav-item:nth-child(3) .nav-link { transition-delay: 0.2s; }
        .sidebar.show .nav-item:nth-child(4) .nav-link { transition-delay: 0.25s; }
        .sidebar.show .nav-item:nth-child(5) .nav-link { transition-delay: 0.3s; }
        .sidebar.show .nav-item:nth-child(6) .nav-link { transition-delay: 0.35s; }
        .sidebar.show .nav-item:nth-child(7) .nav-link { transition-delay: 0.4s; }
        .sidebar.show .nav-item:nth-child(8) .nav-link { transition-delay: 0.45s; }
        .sidebar.show .nav-item:nth-child(9) .nav-link { transition-delay: 0.5s; }
        .sidebar.show .nav-item:nth-child(10) .nav-link { transition-delay: 0.55s; }
        .sidebar.show .nav-item:nth-child(11) .nav-link { transition-delay: 0.6s; }
        .sidebar.show .nav-item:nth-child(12) .nav-link { transition-delay: 0.65s; }

        /* Main Content - Full Width */
        .main-content {
            width: 100%;
            min-height: 100vh;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            margin: 0;
        }

        .main-content.menu-open {
            filter: brightness(0.98);
        }

        .main-content::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.2);
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 9999;
            pointer-events: none;
        }

        .main-content.menu-open::before {
            opacity: 1;
            visibility: visible;
            pointer-events: auto;
        }

        /* Top Navigation */
        .top-nav {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            backdrop-filter: blur(20px);
            padding: 0;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            border-bottom: 1px solid rgba(255, 255, 255, 0.18);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 1000;
            height: 80px;
        }

        .nav-left {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            padding-left: 2rem;
        }

        .nav-toggle {
            background: rgba(255, 255, 255, 0.15);
            border: 2px solid rgba(255, 255, 255, 0.2);
            font-size: 1.5rem;
            color: white;
            cursor: pointer;
            padding: 0.75rem;
            border-radius: 15px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(15px);
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
        }

        .nav-toggle::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s ease;
        }

        .nav-toggle:hover::before {
            left: 100%;
        }

        .nav-toggle:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.4);
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3);
        }

        .nav-toggle:active {
            transform: translateY(-1px) scale(0.98);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }

        .nav-toggle i {
            transition: all 0.3s ease;
            z-index: 1;
            position: relative;
        }

        .nav-toggle:hover i {
            transform: rotate(180deg);
        }

        .nav-toggle.active {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
        }

        .nav-toggle.active i {
            transform: rotate(90deg);
        }

        .nav-toggle-tooltip {
            position: absolute;
            bottom: -35px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 6px;
            font-size: 0.8rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
            pointer-events: none;
        }

        .nav-toggle-tooltip::before {
            content: '';
            position: absolute;
            top: -5px;
            left: 50%;
            transform: translateX(-50%);
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-bottom: 5px solid rgba(0, 0, 0, 0.8);
        }

        .nav-toggle:hover .nav-toggle-tooltip {
            opacity: 1;
            visibility: visible;
            bottom: -40px;
        }

        .breadcrumb-nav {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.95rem;
        }

        .breadcrumb-nav a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .breadcrumb-nav a:hover {
            color: white;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        .breadcrumb-separator {
            color: rgba(255, 255, 255, 0.6);
            margin: 0 0.5rem;
        }

        .nav-center {
            flex: 1;
            max-width: 500px;
            margin: 0 2rem;
        }

        .search-container {
            position: relative;
            width: 100%;
        }

        .search-input {
            width: 100%;
            padding: 1rem 1.5rem 1rem 3.5rem;
            border: none;
            border-radius: 25px;
            font-size: 0.95rem;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .search-input:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.4);
            box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .search-icon {
            position: absolute;
            left: 1.25rem;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.7);
            font-size: 1.1rem;
        }

        /* CSS البحث الذكي */
        .search-filters {
            position: absolute;
            right: 0.5rem;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            gap: 0.25rem;
        }

        .filter-btn {
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: rgba(255, 255, 255, 0.8);
            padding: 0.4rem;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .filter-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            transform: scale(1.1);
        }

        .search-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            max-height: 500px;
            overflow-y: auto;
            display: none;
            margin-top: 0.5rem;
        }

        .search-dropdown.show {
            display: block;
        }

        .search-loading {
            padding: 2rem;
            text-align: center;
            color: #6b7280;
        }

        .search-loading .spin {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .search-suggestions {
            padding: 1rem;
            border-bottom: 1px solid #f3f4f6;
        }

        .suggestions-header {
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.75rem;
            font-size: 0.9rem;
        }

        .suggestions-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .suggestion-item {
            background: #f3f4f6;
            color: #6b7280;
            padding: 0.4rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .suggestion-item:hover {
            background: #667eea;
            color: white;
        }

        .search-results-dropdown {
            max-height: 300px;
            overflow-y: auto;
        }

        .results-header {
            padding: 1rem;
            background: #f8fafc;
            border-bottom: 1px solid #e5e7eb;
            font-size: 0.85rem;
            color: #6b7280;
        }

        .results-list {
            padding: 0.5rem;
        }

        .result-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
        }

        .result-item:hover {
            background: #f8fafc;
            text-decoration: none;
            color: inherit;
        }

        .result-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 0.75rem;
            font-size: 1.1rem;
            color: white;
        }

        .result-content {
            flex: 1;
        }

        .result-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.25rem;
        }

        .result-description {
            font-size: 0.85rem;
            color: #6b7280;
            line-height: 1.4;
        }

        .result-category {
            font-size: 0.75rem;
            color: #9ca3af;
            background: #f3f4f6;
            padding: 0.2rem 0.5rem;
            border-radius: 10px;
            margin-right: 0.5rem;
        }

        .results-footer {
            padding: 1rem;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }

        .view-all-btn {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .view-all-btn:hover {
            color: #5a67d8;
            text-decoration: none;
        }

        .search-categories {
            padding: 1rem;
            border-top: 1px solid #e5e7eb;
        }

        .categories-header {
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.75rem;
            font-size: 0.9rem;
        }

        .categories-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 0.5rem;
        }

        .category-item {
            display: flex;
            align-items: center;
            padding: 0.5rem;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #6b7280;
            font-size: 0.85rem;
        }

        .category-item:hover {
            border-color: #667eea;
            background: #f0f4ff;
            color: #667eea;
            text-decoration: none;
        }

        .category-icon {
            margin-left: 0.5rem;
            font-size: 1rem;
        }

        .nav-right {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            padding-right: 2rem;
        }

        .nav-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .nav-action-btn {
            background: rgba(255, 255, 255, 0.15);
            border: none;
            color: white;
            padding: 0.75rem;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .nav-action-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            animation: pulse-badge 2s infinite;
            box-shadow: 0 0 0 0 rgba(255, 71, 87, 0.7);
        }

        @keyframes pulse-badge {
            0% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(255, 71, 87, 0.7);
            }
            70% {
                transform: scale(1);
                box-shadow: 0 0 0 10px rgba(255, 71, 87, 0);
            }
            100% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(255, 71, 87, 0);
            }
        }

        .user-menu {
            position: relative;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
            cursor: pointer;
            padding: 0.75rem 1.25rem;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .user-info:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .user-avatar {
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.1rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .user-details {
            color: white;
        }

        .user-name {
            font-weight: 700;
            font-size: 0.95rem;
            margin-bottom: 0.25rem;
        }

        .user-role {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .user-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.2);
            padding: 1rem 0;
            min-width: 250px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            z-index: 1001;
            margin-top: 0.5rem;
        }

        .user-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.75rem 1.5rem;
            color: #333;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .dropdown-item:hover {
            background: #f8f9fa;
            color: #667eea;
            text-decoration: none;
        }

        .dropdown-divider {
            height: 1px;
            background: #e9ecef;
            margin: 0.5rem 0;
        }

        /* Notifications Dropdown */
        .notifications-dropdown, .messages-dropdown, .settings-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.2);
            padding: 0;
            min-width: 350px;
            max-width: 400px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            z-index: 1001;
            margin-top: 0.5rem;
            max-height: 500px;
            overflow-y: auto;
        }

        .notifications-dropdown.show, .messages-dropdown.show, .settings-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
        }

        .dropdown-header h6 {
            margin: 0;
            font-weight: 700;
            font-size: 1.1rem;
        }

        .dropdown-body {
            padding: 0;
            max-height: 350px;
            overflow-y: auto;
        }

        .notification-item, .message-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #f3f4f6;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .notification-item:hover, .message-item:hover {
            background: #f8fafc;
            transform: translateX(5px);
            border-left: 3px solid #667eea;
        }

        .notification-item:last-child, .message-item:last-child {
            border-bottom: none;
        }

        .notification-icon, .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.1rem;
            color: white;
            flex-shrink: 0;
        }

        .notification-content, .message-content {
            flex: 1;
        }

        .notification-title, .message-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.25rem;
            font-size: 0.9rem;
        }

        .notification-text, .message-text {
            color: #6b7280;
            font-size: 0.85rem;
            line-height: 1.4;
            margin-bottom: 0.25rem;
        }

        .notification-time, .message-time {
            color: #9ca3af;
            font-size: 0.75rem;
        }

        .notification-unread {
            background: #f0f9ff;
            border-left: 3px solid #3b82f6;
        }

        .message-unread {
            background: #f0fdf4;
            border-left: 3px solid #10b981;
        }

        .dropdown-footer {
            padding: 1rem 1.5rem;
            border-top: 1px solid #e9ecef;
            text-align: center;
        }

        .dropdown-footer a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .dropdown-footer a:hover {
            color: #764ba2;
            text-decoration: none;
        }

        /* Settings Dropdown */
        .settings-dropdown {
            min-width: 280px;
        }

        .setting-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #f3f4f6;
        }

        .setting-item:last-child {
            border-bottom: none;
        }

        .setting-label {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-weight: 600;
            color: #374151;
        }

        .setting-control {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* Toggle Switch */
        .toggle-switch {
            position: relative;
            width: 44px;
            height: 24px;
            background: #d1d5db;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .toggle-switch.active {
            background: #667eea;
        }

        .toggle-switch::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .toggle-switch.active::before {
            transform: translateX(20px);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .notifications-dropdown, .messages-dropdown, .settings-dropdown {
                min-width: 300px;
                max-width: 320px;
                right: -50px;
            }
        }

        .system-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #2ecc71;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(46, 204, 113, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(46, 204, 113, 0); }
            100% { box-shadow: 0 0 0 0 rgba(46, 204, 113, 0); }
        }

        /* Content Area */
        .content-area {
            padding: 2rem;
        }

        .page-header {
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: #6c757d;
            font-size: 1rem;
        }

        /* Cards */
        .card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        /* Buttons */
        .btn {
            border-radius: 0.5rem;
            font-weight: 600;
            padding: 0.5rem 1.5rem;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background: #0b5ed7;
            border-color: #0a58ca;
            transform: translateY(-1px);
        }

        /* Tables */
        .table {
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .table thead th {
            background: var(--primary-color);
            color: white;
            border: none;
            font-weight: 600;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
            }
            
            .nav-toggle {
                display: block;
            }

            .nav-center {
                display: none;
            }

            .nav-actions {
                gap: 0.5rem;
            }

            .nav-left, .nav-right {
                padding-left: 1rem;
                padding-right: 1rem;
            }

            .breadcrumb-nav {
                display: none;
            }

            .user-details {
                display: none;
            }
            
            .content-area {
                padding: 1rem;
            }

            /* Dropdown Menu Responsive */
            .main-menu-dropdown {
                width: 95vw;
                right: 2.5vw;
                left: 2.5vw;
                top: 70px;
                max-height: 85vh;
            }

            .main-menu-dropdown::after {
                right: 50px;
            }

            .menu-nav-link {
                padding: 1rem 1.25rem;
                font-size: 1rem;
            }

            .menu-nav-link i {
                font-size: 1.3rem;
                margin-left: 1.25rem;
            }
        }

        @media (max-width: 480px) {
            .main-menu-dropdown {
                width: 98vw;
                right: 1vw;
                left: 1vw;
                top: 65px;
                border-radius: 15px;
            }

            .menu-dropdown-header h3 {
                font-size: 1.3rem;
            }

            .menu-nav-link {
                padding: 0.875rem 1rem;
                font-size: 0.95rem;
            }
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-out;
        }

        /* Custom Scrollbar */
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255,255,255,0.5);
        }

        /* Notification Badge */
        .notification-badge {
            position: absolute;
            top: -5px;
            left: -5px;
            background: var(--danger-color);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            z-index: 10;
        }

        /* تأثيرات الإشعارات */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); box-shadow: 0 0 20px rgba(255, 71, 87, 0.6); }
            100% { transform: scale(1); }
        }

        @keyframes bounce {
            0%, 20%, 60%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            80% { transform: translateY(-5px); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        @keyframes glow {
            0% { box-shadow: 0 0 5px rgba(255, 71, 87, 0.5); }
            50% { box-shadow: 0 0 20px rgba(255, 71, 87, 0.8), 0 0 30px rgba(255, 71, 87, 0.6); }
            100% { box-shadow: 0 0 5px rgba(255, 71, 87, 0.5); }
        }

        .notification-item.new-notification {
            animation: slideInRight 0.5s ease-out, glow 2s ease-in-out;
            border-left: 4px solid #ff4757;
        }

        @keyframes slideInRight {
            0% { transform: translateX(100%); opacity: 0; }
            100% { transform: translateX(0); opacity: 1; }
        }

        .urgent-notification {
            animation: shake 0.5s ease-in-out 3;
        }

        .warning-notification {
            animation: pulse 1s ease-in-out 2;
        }

        /* تأثيرات مودال الترحيب */
        @keyframes fadeIn {
            0% { opacity: 0; }
            100% { opacity: 1; }
        }

        @keyframes fadeOut {
            0% { opacity: 1; }
            100% { opacity: 0; }
        }

        @keyframes slideInUp {
            0% { transform: translateY(50px); opacity: 0; }
            100% { transform: translateY(0); opacity: 1; }
        }

        /* Loading Spinner */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
    




    <!-- CSS لزر تسجيل الخروج -->
    <style>
        /* تحسين مظهر زر تسجيل الخروج */
        .logout-btn:hover {
            background: rgba(220, 53, 69, 0.1) !important;
            color: #dc3545 !important;
            transform: translateX(-2px);
        }

        .logout-btn:active {
            transform: translateX(0);
            background: rgba(220, 53, 69, 0.2) !important;
        }

        /* تأكيد تسجيل الخروج */
        .logout-btn:focus {
            outline: 2px solid rgba(220, 53, 69, 0.3);
            outline-offset: 2px;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Modern Dropdown Menu -->
    <div class="main-menu-dropdown" id="mainMenuDropdown">
        <div class="menu-dropdown-header">
            <h3>{% if user_language == 'en' %}Osaric{% else %}أوساريك{% endif %}</h3>
            <p>{% if user_language == 'en' %}Advanced Accounting & Inventory Management System{% else %}نظام إدارة الحسابات والمخزون المتطور{% endif %}</p>
        </div>

        <div class="menu-dropdown-content">
            <!-- الرئيسية -->
            <div class="menu-nav-item">
                <a href="{% url 'dashboard_home' %}" class="menu-nav-link {% if request.resolver_match.url_name == 'dashboard_home' %}active{% endif %}">
                    <i class="bi bi-house-door-fill"></i>
                    <span>{% if user_language == 'en' %}Dashboard{% else %}الرئيسية{% endif %}</span>
                </a>
            </div>

            <!-- التعريفات -->
            <div class="menu-nav-item">
                <a href="{% url 'definitions:dashboard' %}" class="menu-nav-link {% if request.resolver_match.namespace == 'definitions' %}active{% endif %}">
                    <i class="bi bi-gear-fill"></i>
                    <span>{% if user_language == 'en' %}Definitions{% else %}التعريفات{% endif %}</span>
                </a>
            </div>

            <!-- ادارة المخازن -->
            <div class="menu-nav-item">
                <a href="{% url 'warehouses:dashboard' %}" class="menu-nav-link {% if request.resolver_match.namespace == 'warehouses' %}active{% endif %}">
                    <i class="bi bi-boxes"></i>
                    <span>{% if user_language == 'en' %}Warehouse Management{% else %}ادارة المخازن{% endif %}</span>
                </a>
            </div>

            <!-- التصنيع -->
            <div class="menu-nav-item">
                <a href="{% url 'manufacturing:dashboard' %}" class="menu-nav-link {% if request.resolver_match.namespace == 'manufacturing' %}active{% endif %}">
                    <i class="bi bi-gear-wide-connected"></i>
                    <span>{% if user_language == 'en' %}Manufacturing{% else %}التصنيع{% endif %}</span>
                </a>
            </div>

            <!-- المبيعات -->
            <div class="menu-nav-item">
                <a href="{% url 'sales:dashboard' %}" class="menu-nav-link {% if request.resolver_match.namespace == 'sales' %}active{% endif %}">
                    <i class="bi bi-cart-plus-fill"></i>
                    <span>{% if user_language == 'en' %}Sales{% else %}المبيعات{% endif %}</span>
                </a>
            </div>

            <!-- المشتريات -->
            <div class="menu-nav-item has-submenu">
                <a href="{% url 'purchases:dashboard' %}" class="menu-nav-link {% if request.resolver_match.namespace == 'purchases' %}active{% endif %}">
                    <i class="bi bi-cart-check-fill"></i>
                    <span>{% if user_language == 'en' %}Purchases{% else %}المشتريات{% endif %}</span>
                    <i class="bi bi-chevron-down submenu-arrow"></i>
                </a>
                <div class="submenu">
                    <a href="{% url 'purchases:dashboard' %}" class="submenu-link">
                        <i class="bi bi-speedometer2"></i>
                        <span>لوحة التحكم</span>
                    </a>
                    <a href="{% url 'purchases:supplier_list' %}" class="submenu-link">
                        <i class="bi bi-building"></i>
                        <span>الموردين</span>
                    </a>
                    <a href="{% url 'purchases:purchase_order_list' %}" class="submenu-link">
                        <i class="bi bi-file-earmark-text"></i>
                        <span>أوامر الشراء</span>
                    </a>
                    <a href="{% url 'purchases:purchase_invoice_list' %}" class="submenu-link">
                        <i class="bi bi-receipt"></i>
                        <span>فواتير الشراء</span>
                    </a>
                    <a href="{% url 'purchases:financial_tracking' %}" class="submenu-link">
                        <i class="bi bi-graph-up-arrow"></i>
                        <span>المتابعة المالية</span>
                    </a>
                </div>
            </div>

            <!-- الاصول الثابته -->
            <div class="menu-nav-item">
                <a href="{% url 'assets:dashboard' %}" class="menu-nav-link {% if request.resolver_match.namespace == 'assets' %}active{% endif %}">
                    <i class="bi bi-building-fill"></i>
                    <span>{% if user_language == 'en' %}Fixed Assets{% else %}الاصول الثابته{% endif %}</span>
                </a>
            </div>

            <!-- البنوك -->
            <div class="menu-nav-item">
                <a href="{% url 'banks:dashboard' %}" class="menu-nav-link {% if request.resolver_match.namespace == 'banks' %}active{% endif %}">
                    <i class="bi bi-bank2"></i>
                    <span>{% if user_language == 'en' %}Banks{% else %}البنوك{% endif %}</span>
                </a>
            </div>

            <!-- الخزائن -->
            <div class="menu-nav-item">
                <a href="{% url 'treasuries:dashboard' %}" class="menu-nav-link {% if request.resolver_match.namespace == 'treasuries' %}active{% endif %}">
                    <i class="bi bi-safe-fill"></i>
                    <span>{% if user_language == 'en' %}Treasuries{% else %}الخزائن{% endif %}</span>
                </a>
            </div>

            <!-- الحسابات العامه -->
            <div class="menu-nav-item">
                <a href="{% url 'accounting:dashboard' %}" class="menu-nav-link {% if request.resolver_match.namespace == 'accounting' %}active{% endif %}">
                    <i class="bi bi-calculator-fill"></i>
                    <span>{% if user_language == 'en' %}General Accounts{% else %}الحسابات العامه{% endif %}</span>
                </a>
            </div>

            <!-- المركز الرئيسى والفروع -->
            <div class="menu-nav-item">
                <a href="{% url 'branches:dashboard' %}" class="menu-nav-link {% if request.resolver_match.namespace == 'branches' %}active{% endif %}">
                    <i class="bi bi-diagram-3-fill"></i>
                    <span>{% if user_language == 'en' %}Head Office & Branches{% else %}المركز الرئيسى والفروع{% endif %}</span>
                </a>
            </div>

            <!-- شؤون العاملين -->
            <div class="menu-nav-item">
                <a href="{% url 'hr:dashboard' %}" class="menu-nav-link {% if request.resolver_match.namespace == 'hr' %}active{% endif %}">
                    <i class="bi bi-people-fill"></i>
                    <span>{% if user_language == 'en' %}Human Resources{% else %}شؤون العاملين{% endif %}</span>
                </a>
            </div>

            <!-- التقارير -->
            <div class="menu-nav-item">
                <a href="{% url 'reports:dashboard' %}" class="menu-nav-link {% if request.resolver_match.namespace == 'reports' %}active{% endif %}">
                    <i class="bi bi-graph-up-arrow"></i>
                    <span>{% if user_language == 'en' %}Reports{% else %}التقارير{% endif %}</span>
                </a>
            </div>

            <!-- الاعدادات والخدمات -->
            <div class="menu-nav-item">
                <a href="{% url 'system_settings:dashboard' %}" class="menu-nav-link {% if request.resolver_match.namespace == 'system_settings' %}active{% endif %}">
                    <i class="bi bi-gear-wide-connected"></i>
                    <span>{% if user_language == 'en' %}Settings & Services{% else %}الاعدادات والخدمات{% endif %}</span>
                </a>
            </div>


        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Top Navigation -->
        <nav class="top-nav">
            <div class="nav-left">
                <button class="nav-toggle" id="navToggle" title="{% if user_language == 'en' %}Open/Close Sidebar (Ctrl+B){% else %}فتح/إغلاق القائمة الجانبية (Ctrl+B){% endif %}" data-bs-toggle="tooltip" data-bs-placement="bottom">
                    <i class="bi bi-list"></i>
                    <span class="nav-toggle-tooltip">{% if user_language == 'en' %}Sidebar{% else %}القائمة الجانبية{% endif %}</span>
                </button>

                <div class="breadcrumb-nav">
                    <a href="{% url 'dashboard_home' %}">
                        <i class="bi bi-house"></i>
                    </a>
                    <span class="breadcrumb-separator">
                        <i class="bi bi-chevron-left"></i>
                    </span>
                    <span>{% block breadcrumb %}{% if user_language == 'en' %}Home{% else %}الرئيسية{% endif %}{% endblock %}</span>
                </div>
            </div>

            <div class="nav-center">
                <div class="search-container">
                    <i class="bi bi-search search-icon"></i>
                    <input type="text" class="search-input" id="smartSearchInput"
                           placeholder="{% if user_language == 'en' %}Smart search in system...{% else %}البحث الذكي في النظام...{% endif %}"
                           onkeyup="performSmartSearch(this.value)"
                           onfocus="showSearchDropdown()"
                           autocomplete="off">
                    <div class="search-filters">
                        <button class="filter-btn" onclick="toggleAdvancedSearch()" title="{% if user_language == 'en' %}Advanced Search{% else %}البحث المتقدم{% endif %}">
                            <i class="bi bi-funnel"></i>
                        </button>
                        <button class="filter-btn" onclick="showSearchHistory()" title="{% if user_language == 'en' %}Search History{% else %}تاريخ البحث{% endif %}">
                            <i class="bi bi-clock-history"></i>
                        </button>
                    </div>

                    <!-- نتائج البحث المنسدلة -->
                    <div class="search-dropdown" id="searchDropdown">
                        <div class="search-loading" id="searchLoading" style="display: none;">
                            <i class="bi bi-arrow-clockwise spin"></i>
                            <span>{% if user_language == 'en' %}Searching...{% else %}جاري البحث...{% endif %}</span>
                        </div>

                        <div class="search-suggestions" id="searchSuggestions">
                            <div class="suggestions-header">{% if user_language == 'en' %}Search Suggestions{% else %}اقتراحات البحث{% endif %}</div>
                            <div class="suggestions-list" id="suggestionsList"></div>
                        </div>

                        <div class="search-results-dropdown" id="searchResultsDropdown">
                            <div class="results-header">
                                {% if user_language == 'en' %}
                                    <span id="resultsCount">0</span> results in
                                    <span id="searchTime">0</span> seconds
                                {% else %}
                                    <span id="resultsCount">0</span> نتيجة في
                                    <span id="searchTime">0</span> ثانية
                                {% endif %}
                            </div>
                            <div class="results-list" id="resultsList"></div>
                            <div class="results-footer">
                                <a href="#" onclick="viewAllResults()" class="view-all-btn">
                                    {% if user_language == 'en' %}View All Results{% else %}عرض جميع النتائج{% endif %}
                                </a>
                            </div>
                        </div>

                        <div class="search-categories" id="searchCategories">
                            <div class="categories-header">{% if user_language == 'en' %}Search by Category{% else %}البحث حسب الفئة{% endif %}</div>
                            <div class="categories-list" id="categoriesList"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="nav-right">
                <div class="nav-actions">
                    <!-- إشعارات -->
                    <div style="position: relative;">
                        <button class="nav-action-btn" title="{% if user_language == 'en' %}Notifications{% else %}الإشعارات{% endif %}" onclick="toggleNotifications()">
                            <i class="bi bi-bell"></i>
                            <span class="notification-badge" id="notificationCount" style="display: none;">0</span>
                        </button>

                        <div class="notifications-dropdown" id="notificationsDropdown">
                            <div class="dropdown-header">
                                <h6>{% if user_language == 'en' %}Notifications{% else %}الإشعارات{% endif %}</h6>
                                <div style="margin-top: 0.5rem;">
                                    <input type="text" placeholder="{% if user_language == 'en' %}Search notifications...{% else %}البحث في الإشعارات...{% endif %}"
                                           style="width: 100%; padding: 0.5rem; border: 1px solid rgba(255,255,255,0.3); border-radius: 8px; background: rgba(255,255,255,0.1); color: white; font-size: 0.85rem;"
                                           onkeyup="searchNotifications(this.value)">
                                </div>
                            </div>
                            <div class="dropdown-body">
                                <!-- سيتم تحميل الإشعارات الحقيقية هنا -->
                                <div id="noNotificationsMessage" style="text-align: center; padding: 2rem; color: #6b7280;">
                                    <i class="bi bi-bell" style="font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
                                    <div>{% if user_language == 'en' %}No new notifications{% else %}لا توجد إشعارات جديدة{% endif %}</div>
                                </div>
                            </div>
                            <div class="dropdown-footer">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <a href="#" onclick="markAllNotificationsAsRead()">{% if user_language == 'en' %}Mark all as read{% else %}تعيين الكل كمقروء{% endif %}</a>
                                    <a href="/notifications/">{% if user_language == 'en' %}View all notifications{% else %}عرض جميع الإشعارات{% endif %}</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الرسائل -->
                    <div style="position: relative;">
                        <button class="nav-action-btn" title="{% if user_language == 'en' %}Messages{% else %}الرسائل{% endif %}" onclick="toggleMessages()">
                            <i class="bi bi-chat-dots"></i>
                            <span class="notification-badge" id="messageCount">5</span>
                        </button>

                        <div class="messages-dropdown" id="messagesDropdown">
                            <div class="dropdown-header">
                                <h6>{% if user_language == 'en' %}Messages{% else %}الرسائل{% endif %}</h6>
                                <div style="margin-top: 0.5rem;">
                                    <input type="text" placeholder="{% if user_language == 'en' %}Search messages...{% else %}البحث في الرسائل...{% endif %}"
                                           style="width: 100%; padding: 0.5rem; border: 1px solid rgba(255,255,255,0.3); border-radius: 8px; background: rgba(255,255,255,0.1); color: white; font-size: 0.85rem;"
                                           onkeyup="searchMessages(this.value)">
                                </div>
                            </div>
                            <div class="dropdown-body">
                                <div class="message-item message-unread">
                                    <div class="message-avatar" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                                        أ
                                    </div>
                                    <div class="message-content">
                                        <div class="message-title">{% if user_language == 'en' %}Ahmed Mohamed{% else %}أحمد محمد{% endif %}</div>
                                        <div class="message-text">{% if user_language == 'en' %}Hello, I want to inquire about the availability of a specific product...{% else %}مرحباً، أريد الاستفسار عن توفر منتج معين...{% endif %}</div>
                                        <div class="message-time">{% if user_language == 'en' %}10 minutes ago{% else %}منذ 10 دقائق{% endif %}</div>
                                    </div>
                                </div>

                                <div class="message-item message-unread">
                                    <div class="message-avatar" style="background: linear-gradient(135deg, #f093fb, #f5576c);">
                                        س
                                    </div>
                                    <div class="message-content">
                                        <div class="message-title">سارة أحمد</div>
                                        <div class="message-text">شكراً لكم على الخدمة الممتازة والتوصيل السريع</div>
                                        <div class="message-time">منذ 30 دقيقة</div>
                                    </div>
                                </div>

                                <div class="message-item">
                                    <div class="message-avatar" style="background: linear-gradient(135deg, #4facfe, #00f2fe);">
                                        م
                                    </div>
                                    <div class="message-content">
                                        <div class="message-title">محمد علي</div>
                                        <div class="message-text">متى سيتم توفر الطلبية رقم #5678؟</div>
                                        <div class="message-time">منذ ساعة</div>
                                    </div>
                                </div>

                                <div class="message-item">
                                    <div class="message-avatar" style="background: linear-gradient(135deg, #43e97b, #38f9d7);">
                                        ف
                                    </div>
                                    <div class="message-content">
                                        <div class="message-title">فاطمة حسن</div>
                                        <div class="message-text">أحتاج عرض سعر لكمية كبيرة من المنتجات</div>
                                        <div class="message-time">منذ 3 ساعات</div>
                                    </div>
                                </div>

                                <div class="message-item">
                                    <div class="message-avatar" style="background: linear-gradient(135deg, #fa709a, #fee140);">
                                        ع
                                    </div>
                                    <div class="message-content">
                                        <div class="message-title">عبدالله خالد</div>
                                        <div class="message-text">تم استلام الطلبية بنجاح، شكراً لكم</div>
                                        <div class="message-time">أمس</div>
                                    </div>
                                </div>
                            </div>
                            <div class="dropdown-footer">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <a href="/messages/compose/" style="color: #10b981; font-weight: 600;">
                                        <i class="bi bi-plus-circle me-1"></i>{% if user_language == 'en' %}New Message{% else %}رسالة جديدة{% endif %}
                                    </a>
                                    <a href="#" onclick="openMessagesPage()">{% if user_language == 'en' %}View all messages{% else %}عرض جميع الرسائل{% endif %}</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الإعدادات السريعة -->
                    <div style="position: relative;">
                        <button class="nav-action-btn" title="{% if user_language == 'en' %}Quick Settings{% else %}الإعدادات السريعة{% endif %}" onclick="toggleQuickSettings()">
                            <i class="bi bi-gear"></i>
                        </button>

                        <div class="settings-dropdown" id="settingsDropdown">
                            <div class="dropdown-header">
                                <h6>{% if user_language == 'en' %}Quick Settings{% else %}الإعدادات السريعة{% endif %}</h6>
                            </div>
                            <div class="dropdown-body">
                                <div class="setting-item">
                                    <div class="setting-label">
                                        <i class="bi bi-moon"></i>
                                        {% if user_language == 'en' %}Dark Mode{% else %}الوضع الليلي{% endif %}
                                    </div>
                                    <div class="setting-control">
                                        <div class="toggle-switch" id="darkModeToggle" onclick="toggleDarkMode()"></div>
                                    </div>
                                </div>

                                <div class="setting-item">
                                    <div class="setting-label">
                                        <i class="bi bi-bell"></i>
                                        {% if user_language == 'en' %}Notifications{% else %}الإشعارات{% endif %}
                                    </div>
                                    <div class="setting-control">
                                        <div class="toggle-switch active" id="notificationsToggle" onclick="toggleNotificationSettings()"></div>
                                    </div>
                                </div>

                                <div class="setting-item">
                                    <div class="setting-label">
                                        <i class="bi bi-volume-up"></i>
                                        {% if user_language == 'en' %}Sounds{% else %}الأصوات{% endif %}
                                    </div>
                                    <div class="setting-control">
                                        <div class="toggle-switch active" id="soundsToggle" onclick="toggleSounds()"></div>
                                    </div>
                                </div>

                                <!-- أزرار اختبار الأصوات -->
                                <div class="setting-item" style="flex-direction: column; align-items: stretch;">
                                    <div class="setting-label" style="margin-bottom: 10px;">
                                        <i class="bi bi-music-note"></i>
                                        {% if user_language == 'en' %}Test Notification Sounds{% else %}اختبار أصوات الإشعارات{% endif %}
                                    </div>
                                    <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                                        <button onclick="testNotificationSound('urgent')"
                                                style="padding: 5px 10px; border: none; border-radius: 5px; background: #ef4444; color: white; font-size: 0.8rem; cursor: pointer;">
                                            {% if user_language == 'en' %}🔔 Gentle Alert{% else %}🔔 تنبيه هادئ{% endif %}
                                        </button>
                                        <button onclick="testNotificationSound('warning')"
                                                style="padding: 5px 10px; border: none; border-radius: 5px; background: #f59e0b; color: white; font-size: 0.8rem; cursor: pointer;">
                                            {% if user_language == 'en' %}🔕 Soft Warning{% else %}🔕 تحذير ناعم{% endif %}
                                        </button>
                                        <button onclick="testNotificationSound('info')"
                                                style="padding: 5px 10px; border: none; border-radius: 5px; background: #3b82f6; color: white; font-size: 0.8rem; cursor: pointer;">
                                            {% if user_language == 'en' %}🔊 Quiet Info{% else %}🔊 معلومات هادئة{% endif %}
                                        </button>
                                    </div>
                                </div>

                                <div class="setting-item">
                                    <div class="setting-label">
                                        <i class="bi bi-translate"></i>
                                        {% if user_language == 'en' %}Language{% else %}اللغة{% endif %}
                                    </div>
                                    <div class="setting-control">
                                        <select style="border: 1px solid #d1d5db; border-radius: 6px; padding: 0.25rem 0.5rem; font-size: 0.85rem;" onchange="changeLanguage(this.value)">
                                            <option value="ar">{% if user_language == 'en' %}Arabic{% else %}العربية{% endif %}</option>
                                            <option value="en">{% if user_language == 'en' %}English{% else %}الإنجليزية{% endif %}</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="setting-item">
                                    <div class="setting-label">
                                        <i class="bi bi-palette"></i>
                                        {% if user_language == 'en' %}Theme{% else %}المظهر{% endif %}
                                    </div>
                                    <div class="setting-control">
                                        <select style="border: 1px solid #d1d5db; border-radius: 6px; padding: 0.25rem 0.5rem; font-size: 0.85rem;" onchange="changeTheme(this.value)">
                                            <option value="default">{% if user_language == 'en' %}Default{% else %}افتراضي{% endif %}</option>
                                            <option value="blue">{% if user_language == 'en' %}Blue{% else %}أزرق{% endif %}</option>
                                            <option value="green">{% if user_language == 'en' %}Green{% else %}أخضر{% endif %}</option>
                                            <option value="purple">{% if user_language == 'en' %}Purple{% else %}بنفسجي{% endif %}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="dropdown-footer">
                                <a href="{% url 'system_settings:dashboard' %}" style="color: #10b981; font-weight: 600;">
                                    <i class="bi bi-star me-2"></i>{% if user_language == 'en' %}Permissions Management{% else %}إدارة الصلاحيات{% endif %}
                                </a>
                                <a href="#" onclick="openSettingsPage()">{% if user_language == 'en' %}More Settings{% else %}المزيد من الإعدادات{% endif %}</a>
                            </div>
                        </div>
                    </div>

                    <!-- ملء الشاشة -->
                    <button class="nav-action-btn" id="fullscreenBtn" title="{% if user_language == 'en' %}Fullscreen{% else %}ملء الشاشة{% endif %}" onclick="toggleFullscreen()">
                        <i class="bi bi-arrows-fullscreen"></i>
                    </button>
                </div>

                <div class="system-status">
                    <span class="status-indicator"></span>
                    <span>{% if user_language == 'en' %}Connected{% else %}متصل{% endif %}</span>
                </div>

                <div class="user-menu">
                    <div class="user-info" onclick="toggleUserDropdown()">
                        <div class="user-avatar">
                            {% if user.first_name %}{{ user.first_name.0|upper }}{% elif user.username %}{{ user.username.0|upper }}{% else %}U{% endif %}
                        </div>
                        <div class="user-details">
                            <div class="user-name">{{ user.get_full_name|default:user.username }}</div>
                            <div class="user-role">{% if user.is_superuser %}{% if user_language == 'en' %}System Administrator{% else %}مدير النظام{% endif %}{% else %}{% if user_language == 'en' %}User{% else %}مستخدم{% endif %}{% endif %}</div>
                        </div>
                        <i class="bi bi-chevron-down" style="color: rgba(255, 255, 255, 0.7);"></i>
                    </div>

                    <div class="user-dropdown" id="userDropdown">
                        <a href="{% url 'profile' %}" class="dropdown-item">
                            <i class="bi bi-person"></i>
                            {% if user_language == 'en' %}Profile{% else %}الملف الشخصي{% endif %}
                        </a>
                        <a href="{% url 'settings' %}" class="dropdown-item">
                            <i class="bi bi-gear"></i>
                            {% if user_language == 'en' %}Settings{% else %}الإعدادات{% endif %}
                        </a>
                        <a href="/notifications/" class="dropdown-item">
                            <i class="bi bi-bell"></i>
                            {% if user_language == 'en' %}Notifications{% else %}الإشعارات{% endif %}
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="dropdown-item">
                            <i class="bi bi-question-circle"></i>
                            {% if user_language == 'en' %}Help{% else %}المساعدة{% endif %}
                        </a>
                        <a href="#" class="dropdown-item">
                            <i class="bi bi-info-circle"></i>
                            {% if user_language == 'en' %}About System{% else %}حول النظام{% endif %}
                        </a>
                        <div class="dropdown-divider"></div>
                        <form method="post" action="{% url 'logout' %}" style="margin: 0;">
                            {% csrf_token %}
                            <button type="submit" class="dropdown-item logout-btn" onclick="confirmLogout(event)" style="color: #dc3545; background: none; border: none; width: 100%; text-align: right; padding: 10px 15px; display: flex; align-items: center; gap: 10px; cursor: pointer; transition: all 0.3s ease;">
                                <i class="bi bi-box-arrow-right"></i>
                                {% if user_language == 'en' %}Logout{% else %}تسجيل الخروج{% endif %}
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Content Area -->
        <div class="content-area">
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
            
            {% block content %}{% endblock %}
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Enhanced Menu Dropdown Toggle with Professional Animations
        const navToggle = document.getElementById('navToggle');
        const menuDropdown = document.getElementById('mainMenuDropdown');
        const mainContent = document.getElementById('mainContent');
        let menuOpen = false;

        // Enhanced click sound effect
        function playClickSound() {
            try {
                // Create a more pleasant click sound
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);

                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.1);
            } catch (e) {
                // Fallback to simple beep
                try {
                    const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                    audio.volume = 0.1;
                    audio.play().catch(() => {});
                } catch (e2) {}
            }
        }

        // Add visual feedback with screen flash
        function addScreenFlash() {
            const flash = document.createElement('div');
            flash.style.position = 'fixed';
            flash.style.top = '0';
            flash.style.left = '0';
            flash.style.width = '100%';
            flash.style.height = '100%';
            flash.style.background = 'rgba(255, 255, 255, 0.1)';
            flash.style.pointerEvents = 'none';
            flash.style.zIndex = '9999';
            flash.style.opacity = '0';
            flash.style.transition = 'opacity 0.1s ease';

            document.body.appendChild(flash);

            setTimeout(() => {
                flash.style.opacity = '1';
                setTimeout(() => {
                    flash.style.opacity = '0';
                    setTimeout(() => {
                        flash.remove();
                    }, 100);
                }, 50);
            }, 10);
        }

        // Enhanced toggle function with professional animations
        function toggleMenu() {
            playClickSound();
            addScreenFlash();

            menuOpen = !menuOpen;
            navToggle.classList.toggle('active', menuOpen);
            menuDropdown.classList.toggle('show', menuOpen);

            // Add ripple effect
            createRippleEffect(navToggle);

            // Animate main content with enhanced effects
            if (menuOpen) {
                mainContent.classList.add('menu-open');
                if (window.innerWidth <= 768) {
                    document.body.style.overflow = 'hidden';
                }

                // Add entrance animation to menu header
                setTimeout(() => {
                    const menuHeader = menuDropdown.querySelector('.menu-dropdown-header');
                    if (menuHeader) {
                        menuHeader.style.animation = 'slideInRight 0.5s ease-out';
                    }
                }, 100);

            } else {
                mainContent.classList.remove('menu-open');
                document.body.style.overflow = 'auto';
            }

            // Enhanced haptic feedback
            if ('vibrate' in navigator) {
                if (menuOpen) {
                    navigator.vibrate([50, 30, 50]); // Double vibration for open
                } else {
                    navigator.vibrate(30); // Single vibration for close
                }
            }

            // Store state in localStorage
            localStorage.setItem('menuOpen', menuOpen);

            // Update button tooltip
            const tooltip = navToggle.querySelector('.nav-toggle-tooltip');
            if (tooltip) {
                {% if user_language == 'en' %}
                    tooltip.textContent = menuOpen ? 'Close Menu' : 'Open Menu';
                {% else %}
                    tooltip.textContent = menuOpen ? 'إغلاق القائمة' : 'فتح القائمة';
                {% endif %}
            }
        }

        // Create ripple effect
        function createRippleEffect(element) {
            const ripple = document.createElement('span');
            const rect = element.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = '50%';
            ripple.style.top = '50%';
            ripple.style.transform = 'translate(-50%, -50%)';
            ripple.style.position = 'absolute';
            ripple.style.borderRadius = '50%';
            ripple.style.background = 'rgba(255, 255, 255, 0.6)';
            ripple.style.animation = 'ripple 0.6s ease-out';
            ripple.style.pointerEvents = 'none';
            ripple.style.zIndex = '1000';

            element.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        }

        // Add enhanced animation CSS
        const animationStyle = document.createElement('style');
        animationStyle.textContent = `
            @keyframes ripple {
                0% {
                    transform: translate(-50%, -50%) scale(0);
                    opacity: 1;
                }
                100% {
                    transform: translate(-50%, -50%) scale(2);
                    opacity: 0;
                }
            }

            @keyframes slideInRight {
                0% {
                    transform: translateX(30px);
                    opacity: 0;
                }
                100% {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @keyframes bounce {
                0%, 20%, 53%, 80%, 100% {
                    transform: translate3d(0,0,0);
                }
                40%, 43% {
                    transform: translate3d(0, -8px, 0);
                }
                70% {
                    transform: translate3d(0, -4px, 0);
                }
                90% {
                    transform: translate3d(0, -2px, 0);
                }
            }

            @keyframes glow {
                0% {
                    box-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
                }
                50% {
                    box-shadow: 0 0 20px rgba(255, 255, 255, 0.6);
                }
                100% {
                    box-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
                }
            }
        `;
        document.head.appendChild(animationStyle);

        // Event listeners
        navToggle.addEventListener('click', toggleMenu);

        // Keyboard shortcut (Ctrl + B)
        document.addEventListener('keydown', function(event) {
            if (event.ctrlKey && event.key === 'b') {
                event.preventDefault();
                toggleMenu();
            }
        });

        // Enhanced outside click detection
        document.addEventListener('click', function(event) {
            if (menuOpen) {
                if (!menuDropdown.contains(event.target) && !navToggle.contains(event.target)) {
                    toggleMenu();
                }
            }
        });

        // Close menu when clicking on menu items
        menuDropdown.addEventListener('click', function(event) {
            if (event.target.classList.contains('menu-nav-link')) {
                setTimeout(() => {
                    toggleMenu();
                }, 150); // Small delay for visual feedback
            }
        });

        // Restore menu state on page load (don't auto-open on page load)
        document.addEventListener('DOMContentLoaded', function() {
            // Menu should be closed by default
            menuOpen = false;
            menuDropdown.classList.remove('show');
            navToggle.classList.remove('active');
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768 && menuOpen) {
                mainContent.classList.remove('menu-open');
                document.body.style.overflow = 'auto';
            }
        });

        // Add floating action button style to nav toggle on mobile
        function updateNavToggleStyle() {
            if (window.innerWidth <= 768) {
                navToggle.style.position = 'fixed';
                navToggle.style.bottom = '20px';
                navToggle.style.left = '20px';
                navToggle.style.top = 'auto';
                navToggle.style.zIndex = '1001';
                navToggle.style.borderRadius = '50%';
                navToggle.style.width = '60px';
                navToggle.style.height = '60px';
                navToggle.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.3)';
            } else {
                navToggle.style.position = 'static';
                navToggle.style.bottom = 'auto';
                navToggle.style.left = 'auto';
                navToggle.style.borderRadius = '15px';
                navToggle.style.width = '50px';
                navToggle.style.height = '50px';
                navToggle.style.boxShadow = 'none';
            }
        }

        // Update style on load and resize
        updateNavToggleStyle();
        window.addEventListener('resize', updateNavToggleStyle);

        // Add pulse animation when menu is closed
        function addPulseAnimation() {
            if (!menuOpen) {
                navToggle.style.animation = 'pulse 2s infinite';
                setTimeout(() => {
                    navToggle.style.animation = 'none';
                }, 6000);
            }
        }

        // Add pulse animation CSS
        const pulseStyle = document.createElement('style');
        pulseStyle.textContent = `
            @keyframes pulse {
                0% {
                    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
                }
                70% {
                    box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
                }
                100% {
                    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
                }
            }
        `;
        document.head.appendChild(pulseStyle);

        // Start pulse animation after page load
        setTimeout(addPulseAnimation, 3000);

        // Add hover effects for menu items
        document.addEventListener('DOMContentLoaded', function() {
            const menuItems = document.querySelectorAll('.menu-nav-link');

            menuItems.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    // Add subtle glow effect
                    this.style.boxShadow = '0 8px 25px rgba(255, 255, 255, 0.1)';

                    // Scale icon
                    const icon = this.querySelector('i');
                    if (icon) {
                        icon.style.transform = 'scale(1.3) rotate(10deg)';
                    }
                });

                item.addEventListener('mouseleave', function() {
                    this.style.boxShadow = '';

                    const icon = this.querySelector('i');
                    if (icon) {
                        icon.style.transform = '';
                    }
                });

                // Add click animation
                item.addEventListener('click', function() {
                    this.style.transform = 'translateX(12px) scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        });

        // Add floating particles effect to menu header
        function createFloatingParticles() {
            const header = document.querySelector('.menu-dropdown-header');
            if (!header) return;

            for (let i = 0; i < 5; i++) {
                const particle = document.createElement('div');
                particle.style.position = 'absolute';
                particle.style.width = '4px';
                particle.style.height = '4px';
                particle.style.background = 'rgba(255, 255, 255, 0.6)';
                particle.style.borderRadius = '50%';
                particle.style.pointerEvents = 'none';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animation = `float ${3 + Math.random() * 2}s ease-in-out infinite`;

                header.appendChild(particle);

                setTimeout(() => {
                    particle.remove();
                }, 5000);
            }
        }

        // Add floating animation CSS
        const floatStyle = document.createElement('style');
        floatStyle.textContent = `
            @keyframes float {
                0%, 100% {
                    transform: translateY(0px) rotate(0deg);
                    opacity: 1;
                }
                50% {
                    transform: translateY(-20px) rotate(180deg);
                    opacity: 0.5;
                }
            }
        `;
        document.head.appendChild(floatStyle);

        // Create particles when menu opens
        navToggle.addEventListener('click', function() {
            if (!menuOpen) { // Will be true after toggle
                setTimeout(createFloatingParticles, 200);
            }
        });

        // Add glow effect on focus
        navToggle.addEventListener('focus', function() {
            this.style.animation = 'glow 1.5s ease-in-out infinite';
        });

        navToggle.addEventListener('blur', function() {
            this.style.animation = 'none';
        });

        // Add bounce effect when sidebar opens
        navToggle.addEventListener('click', function() {
            setTimeout(() => {
                this.style.animation = 'bounce 0.6s ease-out';
                setTimeout(() => {
                    this.style.animation = 'none';
                }, 600);
            }, 100);
        });

        // Initialize tooltips if Bootstrap is available
        if (typeof bootstrap !== 'undefined') {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }

        // Add success notification when menu is used for the first time
        let firstTimeUsed = localStorage.getItem('menuFirstTimeUsed');
        if (!firstTimeUsed) {
            navToggle.addEventListener('click', function() {
                localStorage.setItem('menuFirstTimeUsed', 'true');

                // Show a subtle success message
                const successMsg = document.createElement('div');
                successMsg.style.position = 'fixed';
                successMsg.style.top = '20px';
                successMsg.style.left = '50%';
                successMsg.style.transform = 'translateX(-50%)';
                successMsg.style.background = 'rgba(40, 167, 69, 0.9)';
                successMsg.style.color = 'white';
                successMsg.style.padding = '0.75rem 1.5rem';
                successMsg.style.borderRadius = '25px';
                successMsg.style.fontSize = '0.9rem';
                successMsg.style.zIndex = '10000';
                successMsg.style.opacity = '0';
                successMsg.style.transition = 'all 0.3s ease';
                {% if user_language == 'en' %}
                    successMsg.innerHTML = '<i class="bi bi-check-circle me-2"></i>New dropdown menu activated successfully!';
                {% else %}
                    successMsg.innerHTML = '<i class="bi bi-check-circle me-2"></i>تم تفعيل القائمة المنسدلة الجديدة بنجاح!';
                {% endif %}

                document.body.appendChild(successMsg);

                setTimeout(() => {
                    successMsg.style.opacity = '1';
                    successMsg.style.transform = 'translateX(-50%) translateY(10px)';
                }, 100);

                setTimeout(() => {
                    successMsg.style.opacity = '0';
                    successMsg.style.transform = 'translateX(-50%) translateY(-10px)';
                    setTimeout(() => {
                        successMsg.remove();
                    }, 300);
                }, 3000);
            }, { once: true });
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);

        // Add loading state to buttons
        document.addEventListener('click', function(event) {
            if (event.target.matches('button[type="submit"], .btn-submit')) {
                const button = event.target;
                const originalText = button.innerHTML;
                {% if user_language == 'en' %}
                    button.innerHTML = '<span class="loading-spinner"></span> Processing...';
                {% else %}
                    button.innerHTML = '<span class="loading-spinner"></span> جاري المعالجة...';
                {% endif %}
                button.disabled = true;
                
                // Re-enable after 3 seconds (fallback)
                setTimeout(function() {
                    button.innerHTML = originalText;
                    button.disabled = false;
                }, 3000);
            }
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });

        // User dropdown functionality
        function toggleUserDropdown() {
            const dropdown = document.getElementById('userDropdown');
            dropdown.classList.toggle('show');
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            const userMenu = document.querySelector('.user-menu');
            const userDropdown = document.getElementById('userDropdown');
            const notificationsDropdown = document.getElementById('notificationsDropdown');
            const messagesDropdown = document.getElementById('messagesDropdown');
            const settingsDropdown = document.getElementById('settingsDropdown');

            // Close user dropdown
            if (userMenu && userDropdown && !userMenu.contains(event.target)) {
                userDropdown.classList.remove('show');
            }

            // Close notifications dropdown
            if (notificationsDropdown && !event.target.closest('[onclick="toggleNotifications()"]') && !notificationsDropdown.contains(event.target)) {
                notificationsDropdown.classList.remove('show');
            }

            // Close messages dropdown
            if (messagesDropdown && !event.target.closest('[onclick="toggleMessages()"]') && !messagesDropdown.contains(event.target)) {
                messagesDropdown.classList.remove('show');
            }

            // Close settings dropdown
            if (settingsDropdown && !event.target.closest('[onclick="toggleQuickSettings()"]') && !settingsDropdown.contains(event.target)) {
                settingsDropdown.classList.remove('show');
            }
        });

        // Dropdown functions
        function toggleNotifications() {
            const dropdown = document.getElementById('notificationsDropdown');
            const messagesDropdown = document.getElementById('messagesDropdown');
            const settingsDropdown = document.getElementById('settingsDropdown');

            // إغلاق القوائم الأخرى
            messagesDropdown.classList.remove('show');
            settingsDropdown.classList.remove('show');

            dropdown.classList.toggle('show');

            // تحديث الإشعارات من الخادم
            if (dropdown.classList.contains('show')) {
                loadNotifications();
            }
        }

        function toggleMessages() {
            const dropdown = document.getElementById('messagesDropdown');
            const notificationsDropdown = document.getElementById('notificationsDropdown');
            const settingsDropdown = document.getElementById('settingsDropdown');

            // إغلاق القوائم الأخرى
            notificationsDropdown.classList.remove('show');
            settingsDropdown.classList.remove('show');

            dropdown.classList.toggle('show');
        }

        function toggleQuickSettings() {
            const dropdown = document.getElementById('settingsDropdown');
            const notificationsDropdown = document.getElementById('notificationsDropdown');
            const messagesDropdown = document.getElementById('messagesDropdown');

            // إغلاق القوائم الأخرى
            notificationsDropdown.classList.remove('show');
            messagesDropdown.classList.remove('show');

            dropdown.classList.toggle('show');
        }

        // متغير لتتبع الإشعارات السابقة
        let previousNotificationCount = 0;
        let lastNotificationIds = new Set();

        // تحديث الإشعارات من الخادم
        function loadNotifications() {
            // تحميل إشعارات المخزون
            fetch('/warehouses/api/notifications/')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.notifications.length > 0) {
                    // فحص الإشعارات الجديدة وتشغيل الأصوات
                    checkForNewNotifications(data.notifications);
                    updateNotificationsDropdown(data.notifications);
                    updateNotificationCount(data.count);
                } else {
                    // إذا لم توجد إشعارات مخزون، جرب النظام العام
                    return fetch('/notifications/get/');
                }
            })
            .then(response => {
                if (response) {
                    return response.json();
                }
            })
            .then(data => {
                if (data) {
                    updateNotificationsDropdown(data.notifications || []);
                    updateNotificationCount(data.unread_count || 0);
                }
            })
            .catch(error => {
                console.error('خطأ في تحميل الإشعارات:', error);
                // في حالة الخطأ، عرض إشعارات افتراضية
                updateNotificationCount(0);
            });
        }

        // فحص الإشعارات الجديدة وتشغيل الأصوات
        function checkForNewNotifications(notifications) {
            const currentNotificationIds = new Set(notifications.map(n => n.id));

            // فحص الإشعارات الجديدة
            notifications.forEach(notification => {
                if (!lastNotificationIds.has(notification.id)) {
                    // إشعار جديد - تشغيل الصوت المناسب
                    playNotificationSoundByType(notification.priority || notification.type);

                    // إشعارات المخزون جانبية فقط - لا توجد رسائل منبثقة

                    // عرض toast notification
                    showToast(`إشعار جديد: ${notification.title}`, getToastType(notification.priority || notification.type));
                }
            });

            // تحديث قائمة الإشعارات السابقة
            lastNotificationIds = currentNotificationIds;
        }

        // تحديد نوع Toast حسب أولوية الإشعار
        function getToastType(priority) {
            switch(priority) {
                case 'urgent':
                case 'danger':
                    return 'error';
                case 'high':
                case 'warning':
                    return 'warning';
                case 'normal':
                case 'info':
                    return 'info';
                default:
                    return 'info';
            }
        }

        function updateNotificationsDropdown(notifications) {
            const dropdownBody = document.querySelector('#notificationsDropdown .dropdown-body');
            if (!dropdownBody) return;

            dropdownBody.innerHTML = '';

            if (notifications.length === 0) {
                dropdownBody.innerHTML = `
                    <div id="noNotificationsMessage" style="text-align: center; padding: 2rem; color: #6b7280;">
                        <i class="bi bi-bell" style="font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
                        <div>لا توجد إشعارات جديدة</div>
                    </div>
                `;
                return;
            }

            notifications.forEach(notification => {
                const notificationItem = document.createElement('div');
                let className = `notification-item notification-unread`;

                // تحديد الألوان والأيقونات حسب نوع الإشعار
                let backgroundColor = '#667eea';
                let iconClass = 'bi-bell';

                if (notification.type === 'danger' || notification.priority === 'urgent') {
                    backgroundColor = 'linear-gradient(135deg, #ef4444, #dc2626)';
                    iconClass = notification.icon || 'bi-x-circle';
                    className += ' urgent-notification';
                } else if (notification.type === 'warning' || notification.priority === 'high') {
                    backgroundColor = 'linear-gradient(135deg, #f59e0b, #d97706)';
                    iconClass = notification.icon || 'bi-exclamation-triangle';
                    className += ' warning-notification';
                } else if (notification.type === 'info' || notification.priority === 'normal') {
                    backgroundColor = 'linear-gradient(135deg, #3b82f6, #2563eb)';
                    iconClass = notification.icon || 'bi-info-circle';
                }

                // إضافة تأثير للإشعارات الجديدة
                if (!lastNotificationIds.has(notification.id)) {
                    className += ' new-notification';
                }

                notificationItem.className = className;

                notificationItem.innerHTML = `
                    <div class="notification-icon" style="background: ${backgroundColor};">
                        <i class="${iconClass}"></i>
                    </div>
                    <div class="notification-content">
                        <div class="notification-title">${notification.title}</div>
                        <div class="notification-text">${notification.message}</div>
                        <div class="notification-time">${notification.time || 'الآن'}</div>
                    </div>
                `;

                notificationItem.onclick = () => {
                    notificationItem.classList.remove('notification-unread');
                    updateNotificationCount();

                    // تأثير بصري عند النقر
                    notificationItem.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        notificationItem.style.transform = 'scale(1)';
                    }, 150);

                    if (notification.url) {
                        setTimeout(() => {
                            window.location.href = notification.url;
                        }, 200);
                    }
                };

                dropdownBody.appendChild(notificationItem);
            });
        }

        function updateNotificationCount(count) {
            const notificationCount = document.getElementById('notificationCount');
            if (notificationCount) {
                const oldCount = parseInt(notificationCount.textContent) || 0;
                notificationCount.textContent = count;
                notificationCount.style.display = count > 0 ? 'flex' : 'none';

                // تأثير وميض إذا زاد العدد
                if (count > oldCount && count > 0) {
                    // وميض زر الإشعارات
                    const notificationBtn = document.querySelector('[onclick="toggleNotifications()"]');
                    if (notificationBtn) {
                        notificationBtn.style.animation = 'pulse 1s ease-in-out 3';
                    }

                    // وميض العداد
                    notificationCount.style.animation = 'bounce 0.6s ease-in-out 2';

                    // إزالة التأثيرات بعد انتهائها
                    setTimeout(() => {
                        if (notificationBtn) notificationBtn.style.animation = '';
                        notificationCount.style.animation = '';
                    }, 3000);
                }
            }
        }

        function markNotificationAsRead(notificationId) {
            fetch(`/notifications/mark-read/${notificationId}/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || '',
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadNotifications(); // إعادة تحميل الإشعارات
                }
            })
            .catch(error => {
                console.error('خطأ في تعيين الإشعار كمقروء:', error);
            });
        }

        function markAllNotificationsAsRead() {
            // تعيين جميع الإشعارات كمقروءة محلياً
            const unreadNotifications = document.querySelectorAll('.notification-item.notification-unread');
            unreadNotifications.forEach(notification => {
                notification.classList.remove('notification-unread');
            });

            // تحديث العداد
            updateNotificationCount();

            // إظهار رسالة نجاح
            showToast('تم تعيين جميع الإشعارات كمقروءة', 'success');

            // يمكن إضافة استدعاء API هنا لاحقاً لحفظ الحالة في قاعدة البيانات
            /*
            fetch('/notifications/mark-all-read/', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || '',
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                } else {
                    showToast('حدث خطأ: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('خطأ في تعيين الإشعارات كمقروءة:', error);
                showToast('حدث خطأ في الاتصال', 'error');
            });
            */
        }

        function openMessagesPage() {
            window.location.href = '/messages/';
        }

        function openSettingsPage() {
            showToast('سيتم فتح صفحة الإعدادات قريباً', 'info');
        }

        // Settings functions
        function toggleDarkMode() {
            const toggle = document.getElementById('darkModeToggle');
            toggle.classList.toggle('active');

            if (toggle.classList.contains('active')) {
                // تفعيل الوضع الليلي
                document.body.style.filter = 'invert(1) hue-rotate(180deg)';
                localStorage.setItem('darkMode', 'true');
                showToast('تم تفعيل الوضع الليلي', 'success');
                console.log('✅ تم تفعيل الوضع الليلي وحفظ الحالة');
            } else {
                // إلغاء الوضع الليلي
                document.body.style.filter = '';
                localStorage.setItem('darkMode', 'false');
                showToast('تم إلغاء الوضع الليلي', 'success');
                console.log('✅ تم إلغاء الوضع الليلي وحفظ الحالة');
            }
        }

        function toggleNotificationSettings() {
            const toggle = document.getElementById('notificationsToggle');
            toggle.classList.toggle('active');

            if (toggle.classList.contains('active')) {
                {% if user_language == 'en' %}
                    showToast('Notifications enabled', 'success');
                {% else %}
                    showToast('تم تفعيل الإشعارات', 'success');
                {% endif %}
            } else {
                {% if user_language == 'en' %}
                    showToast('Notifications disabled', 'warning');
                {% else %}
                    showToast('تم إيقاف الإشعارات', 'warning');
                {% endif %}
            }
        }

        function toggleSounds() {
            const toggle = document.getElementById('soundsToggle');
            toggle.classList.toggle('active');

            if (toggle.classList.contains('active')) {
                {% if user_language == 'en' %}
                    showToast('Sounds enabled', 'success');
                {% else %}
                    showToast('تم تفعيل الأصوات', 'success');
                {% endif %}
            } else {
                {% if user_language == 'en' %}
                    showToast('Sounds disabled', 'warning');
                {% else %}
                    showToast('تم إيقاف الأصوات', 'warning');
                {% endif %}
            }
        }

        function changeLanguage(lang) {
            if (lang === 'en') {
                showToast('Language will be changed to English', 'info');
            } else {
                {% if user_language == 'en' %}
                    showToast('Language changed to Arabic', 'info');
                {% else %}
                    showToast('تم تغيير اللغة إلى العربية', 'info');
                {% endif %}
            }
        }

        function changeTheme(theme) {
            const themes = {
                'default': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                'blue': 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                'green': 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
                'purple': 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)'
            };

            const topNav = document.querySelector('.top-nav');
            if (topNav && themes[theme]) {
                topNav.style.background = themes[theme];
                showToast('تم تغيير المظهر بنجاح', 'success');
            }
        }

        // Toast notification function
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 100px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : type === 'warning' ? '#f59e0b' : '#3b82f6'};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 10px;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
                z-index: 10000;
                font-weight: 600;
                transform: translateX(100%);
                transition: all 0.3s ease;
            `;
            toast.textContent = message;

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 100);

            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 3000);
        }

        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().catch(err => {
                    alert('لا يمكن تفعيل وضع ملء الشاشة: ' + err.message);
                });
            } else {
                document.exitFullscreen();
            }
        }

        // دالة لعرض رسالة ترحيب عند تسجيل الدخول
        function showLoginWelcomeNotification() {
            {% if show_welcome_notification and user_just_logged_in %}
                // عرض رسالة ترحيب فورية عند تسجيل الدخول
                setTimeout(() => {
                    const userName = '{{ user.first_name|default:user.username }}';

                    {% if user_language == 'en' %}
                        showWelcomeModal({
                            title: `Welcome ${userName}!`,
                            message: 'You have successfully logged into Osaric Business Management System. We wish you a productive session!',
                            type: 'success'
                        });
                    {% else %}
                        showWelcomeModal({
                            title: `مرحباً بك ${userName}!`,
                            message: 'تم تسجيل دخولك بنجاح إلى نظام إدارة الأعمال أوساريك. نتمنى لك جلسة عمل مثمرة!',
                            type: 'success'
                        });
                    {% endif %}

                    // تشغيل صوت ترحيب
                    playWelcomeSound();

                }, 1000);
            {% endif %}
        }

        // دالة لعرض مودال ترحيب مع تأثيرات
        function showWelcomeModal(options) {
            // إنشاء مودال ترحيب
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                animation: fadeIn 0.3s ease-out;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 20px;
                    padding: 2rem;
                    max-width: 500px;
                    width: 90%;
                    text-align: center;
                    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                    animation: slideInUp 0.5s ease-out;
                ">
                    <div style="
                        width: 80px;
                        height: 80px;
                        background: linear-gradient(135deg, #667eea, #764ba2);
                        border-radius: 50%;
                        margin: 0 auto 1.5rem;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        animation: bounce 1s ease-in-out infinite;
                    ">
                        <i class="bi bi-person-check" style="font-size: 2rem; color: white;"></i>
                    </div>
                    <h3 style="color: #333; margin-bottom: 1rem; font-weight: 600;">${options.title}</h3>
                    <p style="color: #666; margin-bottom: 2rem; line-height: 1.6;">${options.message}</p>
                    <button onclick="closeWelcomeModal()" style="
                        background: linear-gradient(135deg, #667eea, #764ba2);
                        color: white;
                        border: none;
                        padding: 12px 30px;
                        border-radius: 25px;
                        font-weight: 600;
                        cursor: pointer;
                        transition: all 0.3s ease;
                    " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                        {% if user_language == 'en' %}Continue{% else %}متابعة{% endif %}
                    </button>
                </div>
            `;

            document.body.appendChild(modal);

            // إغلاق المودال تلقائياً بعد 5 ثوان
            setTimeout(() => {
                closeWelcomeModal();
            }, 5000);
        }

        // دالة إغلاق مودال الترحيب
        function closeWelcomeModal() {
            const modal = document.querySelector('[style*="z-index: 10000"]');
            if (modal) {
                modal.style.animation = 'fadeOut 0.3s ease-out';
                setTimeout(() => {
                    modal.remove();
                }, 300);
            }
        }

        // دوال المودال محذوفة - الإشعارات جانبية فقط

        // دالة تشغيل صوت الترحيب
        function playWelcomeSound() {
            // فحص إذا كانت الأصوات مفعلة
            const soundsToggle = document.getElementById('soundsToggle');
            if (!soundsToggle || !soundsToggle.classList.contains('active')) {
                return;
            }

            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();

                // تشغيل نغمة ترحيب جميلة
                const notes = [523.25, 659.25, 783.99, 1046.50]; // C5, E5, G5, C6

                notes.forEach((frequency, index) => {
                    setTimeout(() => {
                        const oscillator = audioContext.createOscillator();
                        const gainNode = audioContext.createGain();

                        oscillator.connect(gainNode);
                        gainNode.connect(audioContext.destination);

                        oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
                        oscillator.type = 'sine';
                        gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
                        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

                        oscillator.start(audioContext.currentTime);
                        oscillator.stop(audioContext.currentTime + 0.5);
                    }, index * 200);
                });
            } catch (error) {
                console.log('تعذر تشغيل صوت الترحيب:', error);
            }
        }

        // دالة لعرض رسالة الترحيب مرة واحدة فقط
        function showWelcomeNotificationOnce() {
            // فحص إذا كانت رسالة الترحيب تم عرضها من قبل
            const welcomeShown = localStorage.getItem('welcomeNotificationShown');

            if (!welcomeShown) {
                // عرض رسالة الترحيب بعد 3 ثوان
                setTimeout(() => {
                    {% if user_language == 'en' %}
                        addRealNotification({
                            title: 'Welcome to Osaric',
                            message: 'Integrated Business Management System - Welcome to your journey towards more effective business management!',
                            type: 'success'
                        });
                    {% else %}
                        addRealNotification({
                            title: 'مرحباً بك في أوساريك',
                            message: 'نظام إدارة الأعمال المتكامل - مرحباً بك في رحلتك نحو إدارة أعمال أكثر فعالية!',
                            type: 'success'
                        });
                    {% endif %}

                    // حفظ أن رسالة الترحيب تم عرضها
                    localStorage.setItem('welcomeNotificationShown', 'true');
                    {% if user_language == 'en' %}
                        console.log('✅ Welcome message displayed and state saved');
                    {% else %}
                        console.log('✅ تم عرض رسالة الترحيب وحفظ الحالة');
                    {% endif %}
                }, 3000);
            } else {
                {% if user_language == 'en' %}
                    console.log('ℹ️ Welcome message already shown - skipped');
                {% else %}
                    console.log('ℹ️ رسالة الترحيب تم عرضها من قبل - تم تخطيها');
                {% endif %}
            }
        }

        // دالة لإعادة تعيين رسالة الترحيب (للاختبار)
        function resetWelcomeNotification() {
            localStorage.removeItem('welcomeNotificationShown');
            {% if user_language == 'en' %}
                console.log('🔄 Welcome message reset - will show next time');
            {% else %}
                console.log('🔄 تم إعادة تعيين رسالة الترحيب - ستظهر في المرة القادمة');
            {% endif %}
        }

        // تأكيد تسجيل الخروج
        function confirmLogout(event) {
            event.preventDefault();

            {% if user_language == 'en' %}
                const confirmMessage = 'Are you sure you want to logout?';
            {% else %}
                const confirmMessage = 'هل أنت متأكد من أنك تريد تسجيل الخروج؟';
            {% endif %}

            if (confirm(confirmMessage)) {
                // إذا وافق المستخدم، قم بإرسال النموذج
                event.target.closest('form').submit();
                console.log('✅ تم تأكيد تسجيل الخروج');
            } else {
                console.log('❌ تم إلغاء تسجيل الخروج');
            }
        }

        // دالة لاستعادة حالة الوضع الليلي عند تحميل الصفحة
        function restoreDarkMode() {
            const darkMode = localStorage.getItem('darkMode');
            console.log('🔍 فحص حالة الوضع الليلي المحفوظة:', darkMode);

            // أيضاً فحص إعدادات المستخدم من قاعدة البيانات
            {% if user_settings and user_settings.dark_mode %}
                const userDarkMode = true;
            {% else %}
                const userDarkMode = false;
            {% endif %}

            // تطبيق الوضع الليلي إذا كان مفعل في localStorage أو في إعدادات المستخدم
            if (darkMode === 'true' || userDarkMode) {
                // تفعيل الوضع الليلي
                document.body.style.filter = 'invert(1) hue-rotate(180deg)';

                // تحديث حالة الزر
                const toggle = document.getElementById('darkModeToggle');
                if (toggle) {
                    toggle.classList.add('active');
                }

                // حفظ الحالة في localStorage
                localStorage.setItem('darkMode', 'true');
                console.log('✅ تم استعادة الوضع الليلي');
            } else {
                // التأكد من إلغاء الوضع الليلي
                document.body.style.filter = '';

                // تحديث حالة الزر
                const toggle = document.getElementById('darkModeToggle');
                if (toggle) {
                    toggle.classList.remove('active');
                }

                console.log('✅ تم استعادة الوضع العادي');
            }
        }















        // Global search functionality
        const globalSearch = document.getElementById('globalSearch');
        if (globalSearch) {
            globalSearch.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    const searchTerm = this.value.trim();
                    if (searchTerm) {
                        {% if user_language == 'en' %}
                            alert('🔍 Searching for: "' + searchTerm + '"\n\nSearch functionality will be developed soon...');
                        {% else %}
                            alert('🔍 البحث عن: "' + searchTerm + '"\n\nسيتم تطوير وظيفة البحث قريباً...');
                        {% endif %}
                    }
                }
            });
        }

        // Add loading animation to nav buttons (except dropdown buttons)
        document.querySelectorAll('.nav-action-btn').forEach(btn => {
            if (!btn.hasAttribute('onclick') || btn.getAttribute('onclick') === 'toggleFullscreen()') {
                btn.addEventListener('click', function() {
                    const icon = this.querySelector('i');
                    if (icon) {
                        const originalClass = icon.className;

                        icon.className = 'bi bi-arrow-clockwise';
                        icon.style.animation = 'spin 0.5s linear';

                        setTimeout(() => {
                            icon.className = originalClass;
                            icon.style.animation = '';
                        }, 500);
                    }
                });
            }
        });

        // Add click handlers for notification and message items
        document.addEventListener('click', function(event) {
            // Handle notification clicks
            if (event.target.closest('.notification-item')) {
                const item = event.target.closest('.notification-item');
                item.classList.remove('notification-unread');

                // Update notification count
                const unreadCount = document.querySelectorAll('.notification-unread').length;
                const notificationCount = document.getElementById('notificationCount');
                notificationCount.textContent = unreadCount;
                if (unreadCount === 0) {
                    notificationCount.style.display = 'none';
                }

                showToast('تم فتح الإشعار', 'info');
            }

            // Handle message clicks
            if (event.target.closest('.message-item')) {
                const item = event.target.closest('.message-item');
                item.classList.remove('message-unread');

                // Update message count
                const unreadCount = document.querySelectorAll('.message-unread').length;
                const messageCount = document.getElementById('messageCount');
                messageCount.textContent = unreadCount;
                if (unreadCount === 0) {
                    messageCount.style.display = 'none';
                }

                showToast('تم فتح الرسالة', 'info');
            }
        });

        // تحديث الرسائل والإشعارات من الخادم
        function updateMessagesFromServer() {
            // تحديث عدد الرسائل
            fetch('/messages/api/unread-count/')
            .then(response => response.json())
            .then(data => {
                const messageCount = document.getElementById('messageCount');
                if (messageCount) {
                    messageCount.textContent = data.count;
                    messageCount.style.display = data.count > 0 ? 'flex' : 'none';
                }
            });

            // تحديث قائمة الرسائل
            fetch('/messages/api/recent-messages/')
            .then(response => response.json())
            .then(data => {
                updateMessagesDropdown(data.messages);
            });
        }

        function updateMessagesDropdown(messages) {
            const dropdownBody = document.querySelector('#messagesDropdown .dropdown-body');
            if (!dropdownBody) return;

            dropdownBody.innerHTML = '';

            if (messages.length === 0) {
                dropdownBody.innerHTML = `
                    <div style="text-align: center; padding: 2rem; color: #6b7280;">
                        <i class="bi bi-inbox" style="font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
                        <div>{% if user_language == 'en' %}No messages{% else %}لا توجد رسائل{% endif %}</div>
                    </div>
                `;
                return;
            }

            messages.forEach(message => {
                const messageItem = document.createElement('div');
                messageItem.className = `message-item ${!message.is_read ? 'message-unread' : ''}`;
                messageItem.innerHTML = `
                    <div class="message-avatar" style="background: ${message.priority_color};">
                        ${message.sender.charAt(0).toUpperCase()}
                    </div>
                    <div class="message-content">
                        <div class="message-title">${message.sender}</div>
                        <div class="message-text">${message.content}</div>
                        <div class="message-time">${message.time}</div>
                    </div>
                `;
                messageItem.onclick = () => {
                    window.location.href = `/messages/message/${message.id}/`;
                };

                // إضافة زر رد سريع
                const replyBtn = document.createElement('button');
                replyBtn.className = 'message-action-btn';
                replyBtn.innerHTML = '<i class="bi bi-reply"></i>';
                {% if user_language == 'en' %}
                    replyBtn.title = 'Quick Reply';
                {% else %}
                    replyBtn.title = 'رد سريع';
                {% endif %}
                replyBtn.style.cssText = 'position: absolute; top: 0.5rem; left: 0.5rem; background: rgba(102, 126, 234, 0.1); color: #667eea; border: none; padding: 0.25rem; border-radius: 4px; cursor: pointer;';
                replyBtn.onclick = (e) => {
                    e.stopPropagation();
                    showQuickReply(message);
                };
                messageItem.style.position = 'relative';
                messageItem.appendChild(replyBtn);
                dropdownBody.appendChild(messageItem);
            });
        }

        // تحديث كل 30 ثانية
        setInterval(updateMessagesFromServer, 30000);

        // تحديث أولي
        setTimeout(updateMessagesFromServer, 2000);
        setTimeout(loadNotifications, 1000);

        // تحديث الإشعارات كل 30 ثانية
        setInterval(() => {
            fetch('/notifications/api/unread-count/')
            .then(response => response.json())
            .then(data => {
                updateNotificationCount(data.count);
            })
            .catch(error => {
                console.error('خطأ في تحديث عدد الإشعارات:', error);
            });
        }, 30000);

        // تم حذف الإشعارات التجريبية - سيتم استخدام الإشعارات الحقيقية فقط



        // تم حذف استدعاء الإشعارات التجريبية

        // إزالة الإشعارات التجريبية - سيتم استخدام الإشعارات الحقيقية فقط
        // setInterval(() => {
        //     const shouldAddNotification = Math.random() < 0.05; // 5% chance every 30 seconds
        //     if (shouldAddNotification) {
        //         addNewNotification();
        //     }
        // }, 30000);

        function addNewNotification() {
            // تم تعطيل الإشعارات التجريبية - سيتم استخدام الإشعارات الحقيقية فقط
            return;
        }

        // دالة لإضافة إشعار حقيقي
        // استخدم هذه الدالة لإضافة إشعارات حقيقية في النظام
        // مثال: addRealNotification({title: 'عنوان', message: 'الرسالة', type: 'success'})
        // الأنواع المتاحة: success, warning, error, info, order, payment, message
        function addRealNotification(notification) {
            const dropdownBody = document.querySelector('#notificationsDropdown .dropdown-body');
            const noNotificationsMessage = document.getElementById('noNotificationsMessage');

            {% if user_language == 'en' %}
                // Hide "no notifications" message
            {% else %}
                // إخفاء رسالة "لا توجد إشعارات"
            {% endif %}
            if (noNotificationsMessage) {
                noNotificationsMessage.style.display = 'none';
            }

            const notificationItem = document.createElement('div');
            notificationItem.className = 'notification-item notification-unread';

            const colors = {
                'success': 'linear-gradient(135deg, #10b981, #059669)',
                'warning': 'linear-gradient(135deg, #f59e0b, #d97706)',
                'error': 'linear-gradient(135deg, #ef4444, #dc2626)',
                'info': 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
                'order': 'linear-gradient(135deg, #8b5cf6, #7c3aed)',
                'payment': 'linear-gradient(135deg, #06b6d4, #0891b2)',
                'message': 'linear-gradient(135deg, #06b6d4, #0891b2)'
            };

            const icons = {
                'success': 'bi-check-circle',
                'warning': 'bi-exclamation-triangle',
                'error': 'bi-x-circle',
                'info': 'bi-info-circle',
                'order': 'bi-cart',
                'payment': 'bi-cash-coin',
                'message': 'bi-chat-dots'
            };

            notificationItem.innerHTML = `
                <div class="notification-icon" style="background: ${colors[notification.type] || colors['info']};">
                    <i class="${icons[notification.type] || icons['info']}"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-title">${notification.title}</div>
                    <div class="notification-text">${notification.message}</div>
                    <div class="notification-time">{% if user_language == 'en' %}Now{% else %}الآن{% endif %}</div>
                </div>
            `;

            notificationItem.onclick = () => {
                notificationItem.classList.remove('notification-unread');
                showToast('تم فتح الإشعار', 'info');
            };

            dropdownBody.insertBefore(notificationItem, dropdownBody.firstChild);

            // تحديث العداد
            const notificationCount = document.getElementById('notificationCount');
            const currentCount = parseInt(notificationCount.textContent) || 0;
            notificationCount.textContent = currentCount + 1;
            notificationCount.style.display = 'flex';

            // إشعار صوتي
            const soundsToggle = document.getElementById('soundsToggle');
            if (soundsToggle && soundsToggle.classList.contains('active')) {
                playNotificationSound();
            }

            // عرض Toast
            {% if user_language == 'en' %}
                showToast('New notification: ' + notification.title, 'info');
            {% else %}
                showToast('إشعار جديد: ' + notification.title, 'info');
            {% endif %}
        }

        // دالة تشغيل صوت الإشعار حسب النوع
        function playNotificationSoundByType(type) {
            // فحص إذا كانت الأصوات مفعلة
            const soundsToggle = document.getElementById('soundsToggle');
            if (!soundsToggle || !soundsToggle.classList.contains('active')) {
                return; // الأصوات معطلة
            }

            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();

                switch(type) {
                    case 'urgent':
                    case 'danger':
                        // صوت تنبيه عاجل - نغمة حادة متكررة
                        playUrgentAlert(audioContext);
                        break;
                    case 'high':
                    case 'warning':
                        // صوت تحذير - نغمة متوسطة
                        playWarningAlert(audioContext);
                        break;
                    case 'normal':
                    case 'info':
                    default:
                        // صوت إشعار عادي - نغمة لطيفة
                        playInfoAlert(audioContext);
                        break;
                }
            } catch (error) {
                console.log('تعذر تشغيل صوت الإشعار:', error);
            }
        }

        // صوت التنبيه العاجل (مخزون نافد) - صافرة هادئة
        function playUrgentAlert(audioContext) {
            // صافرة هادئة ولطيفة للتنبيه العاجل
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            // صافرة هادئة ومريحة
            oscillator.type = 'sine'; // موجة ناعمة
            oscillator.frequency.setValueAtTime(600, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(700, audioContext.currentTime + 0.2);

            // تدرج ناعم وهادئ
            gainNode.gain.setValueAtTime(0, audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(0.15, audioContext.currentTime + 0.05);
            gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 0.4);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.4);
        }

        // صوت التحذير (مخزون منخفض) - صافرة هادئة
        function playWarningAlert(audioContext) {
            // صافرة هادئة للتحذير
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            // صافرة هادئة ومتوسطة
            oscillator.type = 'triangle'; // موجة مثلثية للنعومة
            oscillator.frequency.setValueAtTime(500, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.15);

            // تدرج ناعم وهادئ
            gainNode.gain.setValueAtTime(0, audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(0.12, audioContext.currentTime + 0.04);
            gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 0.3);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);
        }

        // صوت الإشعار العادي (مخزون زائد) - صافرة هادئة جداً
        function playInfoAlert(audioContext) {
            // صافرة هادئة جداً ولطيفة للمعلومات
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            // صافرة هادئة جداً
            oscillator.type = 'sine'; // موجة ناعمة جداً
            oscillator.frequency.setValueAtTime(450, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(500, audioContext.currentTime + 0.1);

            // تدرج ناعم وهادئ جداً
            gainNode.gain.setValueAtTime(0, audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(0.08, audioContext.currentTime + 0.05);
            gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 0.25);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.25);
        }

        // دالة تشغيل صوت خاص للرسائل المنبثقة - صافرة إشعار
        function playModalNotificationSound(priority = 'normal') {
            // فحص إذا كانت الأصوات مفعلة
            const soundsToggle = document.getElementById('soundsToggle');
            if (!soundsToggle || !soundsToggle.classList.contains('active')) {
                return;
            }

            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();

                // صافرة إشعار للرسائل المنبثقة
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                // صافرة إشعار واضحة ومميزة
                oscillator.type = 'square'; // موجة مربعة للوضوح
                oscillator.frequency.setValueAtTime(900, audioContext.currentTime);

                // تدرج سريع مثل الصافرة
                gainNode.gain.setValueAtTime(0, audioContext.currentTime);
                gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 0.01);
                gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 0.2);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.2);
            } catch (error) {
                console.log('تعذر تشغيل صوت الرسالة المنبثقة:', error);
            }
        }

        // دالة تشغيل صوت الإشعار العادية (للتوافق مع الكود القديم)
        function playNotificationSound() {
            playNotificationSoundByType('info');
        }

        // دالة اختبار أصوات الإشعارات
        function testNotificationSound(type) {
            playNotificationSoundByType(type);

            // عرض رسالة تأكيد
            const typeNames = {
                'urgent': '{% if user_language == "en" %}🔔 Gentle Alert Sound{% else %}🔔 صوت تنبيه هادئ{% endif %}',
                'warning': '{% if user_language == "en" %}🔕 Soft Warning Sound{% else %}🔕 صوت تحذير ناعم{% endif %}',
                'info': '{% if user_language == "en" %}🔊 Quiet Info Sound{% else %}🔊 صوت معلومات هادئ{% endif %}'
            };

            showToast(`{% if user_language == "en" %}Playing{% else %}تشغيل{% endif %}: ${typeNames[type]}`, 'info');
        }

        // Search functions
        function searchNotifications(query) {
            const notifications = document.querySelectorAll('#notificationsDropdown .notification-item');
            const searchQuery = query.toLowerCase().trim();

            notifications.forEach(notification => {
                const title = notification.querySelector('.notification-title').textContent.toLowerCase();
                const text = notification.querySelector('.notification-text').textContent.toLowerCase();

                if (searchQuery === '' || title.includes(searchQuery) || text.includes(searchQuery)) {
                    notification.style.display = 'flex';
                } else {
                    notification.style.display = 'none';
                }
            });
        }

        function searchMessages(query) {
            const messages = document.querySelectorAll('#messagesDropdown .message-item');
            const searchQuery = query.toLowerCase().trim();

            messages.forEach(message => {
                const title = message.querySelector('.message-title').textContent.toLowerCase();
                const text = message.querySelector('.message-text').textContent.toLowerCase();

                if (searchQuery === '' || title.includes(searchQuery) || text.includes(searchQuery)) {
                    message.style.display = 'flex';
                } else {
                    message.style.display = 'none';
                }
            });
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(event) {
            // Alt + N for notifications
            if (event.altKey && event.key === 'n') {
                event.preventDefault();
                toggleNotifications();
            }

            // Alt + M for messages
            if (event.altKey && event.key === 'm') {
                event.preventDefault();
                toggleMessages();
            }

            // Alt + S for settings
            if (event.altKey && event.key === 's') {
                event.preventDefault();
                toggleQuickSettings();
            }

            // F11 for fullscreen
            if (event.key === 'F11') {
                event.preventDefault();
                toggleFullscreen();
            }

            // Escape to close all dropdowns
            if (event.key === 'Escape') {
                document.getElementById('notificationsDropdown').classList.remove('show');
                document.getElementById('messagesDropdown').classList.remove('show');
                document.getElementById('settingsDropdown').classList.remove('show');
                document.getElementById('userDropdown').classList.remove('show');
            }
        });

        // Add spin animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);

        // وظيفة الرد السريع
        function showQuickReply(message) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 15px; padding: 2rem; max-width: 500px; width: 90%; max-height: 80vh; overflow-y: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem; border-bottom: 1px solid #e5e7eb; padding-bottom: 1rem;">
                        <h3 style="margin: 0; color: #1f2937;">{% if user_language == 'en' %}Quick Reply{% else %}رد سريع{% endif %}</h3>
                        <button onclick="closeQuickReply()" style="background: none; border: none; font-size: 1.5rem; color: #6b7280; cursor: pointer;">×</button>
                    </div>
                    <div style="margin-bottom: 1rem; padding: 1rem; background: #f8fafc; border-radius: 8px; border-right: 3px solid #667eea;">
                        {% if user_language == 'en' %}
                            <div style="font-weight: 600; color: #374151; margin-bottom: 0.5rem;">From: ${message.sender}</div>
                            <div style="font-weight: 600; color: #374151; margin-bottom: 0.5rem;">Subject: ${message.subject}</div>
                        {% else %}
                            <div style="font-weight: 600; color: #374151; margin-bottom: 0.5rem;">من: ${message.sender}</div>
                            <div style="font-weight: 600; color: #374151; margin-bottom: 0.5rem;">الموضوع: ${message.subject}</div>
                        {% endif %}
                        <div style="color: #6b7280; font-size: 0.9rem;">${message.content}</div>
                    </div>
                    <form onsubmit="sendQuickReply(event, ${message.id})">
                        {% if user_language == 'en' %}
                            <textarea placeholder="Write your reply here..." style="width: 100%; height: 120px; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 8px; resize: vertical; font-family: inherit;" required></textarea>
                            <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 1rem;">
                                <button type="button" onclick="closeQuickReply()" style="padding: 0.5rem 1rem; background: #6b7280; color: white; border: none; border-radius: 6px; cursor: pointer;">Cancel</button>
                                <button type="submit" style="padding: 0.5rem 1rem; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; border-radius: 6px; cursor: pointer;">Send</button>
                            </div>
                        {% else %}
                            <textarea placeholder="اكتب ردك هنا..." style="width: 100%; height: 120px; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 8px; resize: vertical; font-family: inherit;" required></textarea>
                            <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 1rem;">
                                <button type="button" onclick="closeQuickReply()" style="padding: 0.5rem 1rem; background: #6b7280; color: white; border: none; border-radius: 6px; cursor: pointer;">إلغاء</button>
                                <button type="submit" style="padding: 0.5rem 1rem; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; border-radius: 6px; cursor: pointer;">إرسال</button>
                            </div>
                        {% endif %}
                    </form>
                </div>
            `;

            document.body.appendChild(modal);
            modal.querySelector('textarea').focus();
        }

        function closeQuickReply() {
            const modal = document.querySelector('[style*="position: fixed"][style*="z-index: 10000"]');
            if (modal) {
                document.body.removeChild(modal);
            }
        }

        function sendQuickReply(event, messageId) {
            event.preventDefault();
            const textarea = event.target.querySelector('textarea');
            const content = textarea.value.trim();

            if (!content) return;

            // إرسال الرد
            fetch(`/messages/message/${messageId}/reply/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
                },
                body: `content=${encodeURIComponent(content)}`
            })
            .then(response => {
                if (response.ok) {
                    {% if user_language == 'en' %}
                        showToast('Reply sent successfully', 'success');
                    {% else %}
                        showToast('تم إرسال الرد بنجاح', 'success');
                    {% endif %}
                    closeQuickReply();
                    updateMessagesFromServer();
                } else {
                    {% if user_language == 'en' %}
                        showToast('Error occurred while sending reply', 'error');
                    {% else %}
                        showToast('حدث خطأ أثناء إرسال الرد', 'error');
                    {% endif %}
                }
            })
            .catch(error => {
                {% if user_language == 'en' %}
                    showToast('Error occurred while sending reply', 'error');
                {% else %}
                    showToast('حدث خطأ أثناء إرسال الرد', 'error');
                {% endif %}
            });
        }

        // نظام البحث الذكي
        let searchTimeout;
        let currentQuery = '';

        function performSmartSearch(query) {
            clearTimeout(searchTimeout);
            currentQuery = query.trim();

            if (currentQuery.length === 0) {
                hideSearchDropdown();
                return;
            }

            if (currentQuery.length < 2) {
                showSearchSuggestions();
                return;
            }

            showSearchLoading();

            searchTimeout = setTimeout(() => {
                fetch(`/search/api/?q=${encodeURIComponent(currentQuery)}&limit=8`)
                .then(response => response.json())
                .then(data => {
                    hideSearchLoading();
                    displaySearchResults(data);
                })
                .catch(error => {
                    hideSearchLoading();
                    {% if user_language == 'en' %}
                        console.error('Search error:', error);
                    {% else %}
                        console.error('خطأ في البحث:', error);
                    {% endif %}
                });
            }, 300);
        }

        function showSearchDropdown() {
            const dropdown = document.getElementById('searchDropdown');
            dropdown.classList.add('show');

            if (currentQuery.length === 0) {
                showSearchSuggestions();
            }
        }

        function hideSearchDropdown() {
            const dropdown = document.getElementById('searchDropdown');
            dropdown.classList.remove('show');
        }

        function showSearchLoading() {
            document.getElementById('searchLoading').style.display = 'block';
            document.getElementById('searchSuggestions').style.display = 'none';
            document.getElementById('searchResultsDropdown').style.display = 'none';
            document.getElementById('searchCategories').style.display = 'none';
        }

        function hideSearchLoading() {
            document.getElementById('searchLoading').style.display = 'none';
        }

        function showSearchSuggestions() {
            fetch('/search/api/suggestions/')
            .then(response => response.json())
            .then(data => {
                const suggestionsList = document.getElementById('suggestionsList');
                suggestionsList.innerHTML = '';

                data.suggestions.forEach(suggestion => {
                    const item = document.createElement('div');
                    item.className = 'suggestion-item';
                    item.textContent = suggestion.text;
                    item.onclick = () => {
                        document.getElementById('smartSearchInput').value = suggestion.text;
                        performSmartSearch(suggestion.text);
                    };
                    suggestionsList.appendChild(item);
                });

                document.getElementById('searchSuggestions').style.display = 'block';
                document.getElementById('searchResultsDropdown').style.display = 'none';
                document.getElementById('searchCategories').style.display = 'block';
                showSearchCategories();
            });
        }

        function displaySearchResults(data) {
            const resultsList = document.getElementById('resultsList');
            const resultsCount = document.getElementById('resultsCount');
            const searchTime = document.getElementById('searchTime');

            resultsCount.textContent = data.total_count;
            searchTime.textContent = data.execution_time;

            resultsList.innerHTML = '';

            if (data.results.length === 0) {
                resultsList.innerHTML = `
                    <div style="text-align: center; padding: 2rem; color: #6b7280;">
                        <i class="bi bi-search" style="font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
                        {% if user_language == 'en' %}
                            <div>No results found for "${currentQuery}"</div>
                        {% else %}
                            <div>لا توجد نتائج للبحث "${currentQuery}"</div>
                        {% endif %}
                    </div>
                `;
            } else {
                data.results.forEach(result => {
                    const item = document.createElement('a');
                    item.className = 'result-item';
                    item.href = result.url;

                    const iconColors = {
                        'bi-box': '#f59e0b',
                        'bi-people': '#10b981',
                        'bi-receipt': '#3b82f6',
                        'bi-person': '#8b5cf6',
                        'bi-envelope': '#06b6d4',
                        'bi-bell': '#ef4444'
                    };

                    item.innerHTML = `
                        <div class="result-icon" style="background: ${iconColors[result.icon] || '#6b7280'};">
                            <i class="${result.icon}"></i>
                        </div>
                        <div class="result-content">
                            <div class="result-title">${result.highlight || result.title}</div>
                            <div class="result-description">${result.description}</div>
                        </div>
                        <div class="result-category">${result.category}</div>
                    `;

                    resultsList.appendChild(item);
                });
            }

            document.getElementById('searchSuggestions').style.display = 'none';
            document.getElementById('searchResultsDropdown').style.display = 'block';
            document.getElementById('searchCategories').style.display = 'none';
        }

        function showSearchCategories() {
            const categoriesList = document.getElementById('categoriesList');
            {% if user_language == 'en' %}
                const categories = [
                    { name: 'Products', icon: 'bi-box', url: '/products/' },
                    { name: 'Customers', icon: 'bi-people', url: '/customers/' },
                    { name: 'Invoices', icon: 'bi-receipt', url: '/invoices/' },
                    { name: 'Users', icon: 'bi-person', url: '/users/' },
                    { name: 'Messages', icon: 'bi-envelope', url: '/messages/' },
                    { name: 'Notifications', icon: 'bi-bell', url: '/notifications/' }
                ];
            {% else %}
                const categories = [
                    { name: 'المنتجات', icon: 'bi-box', url: '/products/' },
                    { name: 'العملاء', icon: 'bi-people', url: '/customers/' },
                    { name: 'الفواتير', icon: 'bi-receipt', url: '/invoices/' },
                    { name: 'المستخدمين', icon: 'bi-person', url: '/users/' },
                    { name: 'الرسائل', icon: 'bi-envelope', url: '/messages/' },
                    { name: 'الإشعارات', icon: 'bi-bell', url: '/notifications/' }
                ];
            {% endif %}

            categoriesList.innerHTML = '';
            categories.forEach(category => {
                const item = document.createElement('a');
                item.className = 'category-item';
                item.href = category.url;
                item.innerHTML = `
                    <i class="${category.icon} category-icon"></i>
                    ${category.name}
                `;
                categoriesList.appendChild(item);
            });
        }

        function viewAllResults() {
            if (currentQuery) {
                window.location.href = `/search/?q=${encodeURIComponent(currentQuery)}`;
            }
        }

        function toggleAdvancedSearch() {
            {% if user_language == 'en' %}
                showToast('Advanced search coming soon', 'info');
            {% else %}
                showToast('البحث المتقدم قريباً', 'info');
            {% endif %}
        }

        function showSearchHistory() {
            fetch('/search/api/history/')
            .then(response => response.json())
            .then(data => {
                const suggestionsList = document.getElementById('suggestionsList');
                suggestionsList.innerHTML = '';

                if (data.history.length === 0) {
                    {% if user_language == 'en' %}
                        suggestionsList.innerHTML = '<div style="text-align: center; color: #6b7280; padding: 1rem;">No search history</div>';
                    {% else %}
                        suggestionsList.innerHTML = '<div style="text-align: center; color: #6b7280; padding: 1rem;">لا يوجد تاريخ بحث</div>';
                    {% endif %}
                } else {
                    data.history.forEach(item => {
                        const suggestion = document.createElement('div');
                        suggestion.className = 'suggestion-item';
                        suggestion.innerHTML = `${item.query} <small>(${item.results_count})</small>`;
                        suggestion.onclick = () => {
                            document.getElementById('smartSearchInput').value = item.query;
                            performSmartSearch(item.query);
                        };
                        suggestionsList.appendChild(suggestion);
                    });
                }

                document.getElementById('searchSuggestions').style.display = 'block';
                document.getElementById('searchResultsDropdown').style.display = 'none';
                document.getElementById('searchCategories').style.display = 'none';
                showSearchDropdown();
            })
            .catch(error => {
                {% if user_language == 'en' %}
                    showToast('Error loading search history', 'error');
                {% else %}
                    showToast('خطأ في تحميل تاريخ البحث', 'error');
                {% endif %}
            });
        }

        // إغلاق البحث عند النقر خارجه
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.search-container')) {
                hideSearchDropdown();
            }
        });

        // اختصارات لوحة المفاتيح للبحث
        document.addEventListener('keydown', function(event) {
            // Ctrl + K للبحث
            if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
                event.preventDefault();
                document.getElementById('smartSearchInput').focus();
            }

            // Escape لإغلاق البحث
            if (event.key === 'Escape') {
                hideSearchDropdown();
                document.getElementById('smartSearchInput').blur();
            }
        });

        // تحميل الإشعارات عند تحميل الصفحة
        loadNotifications();

        // تحديث الإشعارات كل 30 ثانية
        setInterval(loadNotifications, 30000);

        console.log('🚀 تم تحميل جميع وظائف الشريط العلوي المتقدمة');
        console.log('🔔 تم تفعيل نظام الإشعارات الحقيقية');
        console.log('💬 تم تفعيل نظام الرسائل المتكامل');
        console.log('🔍 تم تفعيل البحث الذكي المتقدم');
        console.log('⌨️ اختصارات لوحة المفاتيح:');
        console.log('   Alt + N: الإشعارات');
        console.log('   Alt + M: الرسائل');
        console.log('   Alt + S: الإعدادات');
        console.log('   Ctrl + K: البحث الذكي');
        console.log('   F11: ملء الشاشة');
        console.log('   Escape: إغلاق القوائم');

        // دالة لتحديث عداد الإشعارات
        function updateNotificationCount() {
            const notificationCount = document.getElementById('notificationCount');
            const unreadNotifications = document.querySelectorAll('.notification-item.notification-unread').length;

            if (unreadNotifications > 0) {
                notificationCount.textContent = unreadNotifications;
                notificationCount.style.display = 'flex';
            } else {
                notificationCount.style.display = 'none';
            }
        }

        // دالة استعادة حجم الخط
        function restoreFontSize() {
            const savedFontSize = localStorage.getItem('fontSize');
            {% if user_settings %}
                const userFontSize = '{{ user_settings.font_size }}';
            {% else %}
                const userFontSize = 'medium';
            {% endif %}

            const fontSize = savedFontSize || userFontSize;

            if (fontSize === 'small') {
                document.documentElement.style.fontSize = '12px';
            } else if (fontSize === 'large') {
                document.documentElement.style.fontSize = '18px';
            } else {
                document.documentElement.style.fontSize = '14px';
            }

            console.log('✅ تم استعادة حجم الخط:', fontSize);
        }

        // تحديث عداد الإشعارات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateNotificationCount();

            // استعادة حالة الوضع الليلي
            restoreDarkMode();

            // استعادة حجم الخط
            restoreFontSize();

            // إضافة إشعار ترحيب عند تسجيل الدخول
            showLoginWelcomeNotification();

            // إضافة إشعار ترحيب (مرة واحدة فقط)
            showWelcomeNotificationOnce();

            // تطبيق الترجمات على placeholders
            applyPlaceholderTranslations();
        });

        // دالة لتطبيق الترجمات على placeholders
        function applyPlaceholderTranslations() {
            const userLanguage = '{{ user_language }}';
            if (userLanguage === 'en') {
                // تطبيق placeholders الإنجليزية
                document.querySelectorAll('[data-placeholder-en]').forEach(element => {
                    const englishPlaceholder = element.getAttribute('data-placeholder-en');
                    if (englishPlaceholder) {
                        element.setAttribute('placeholder', englishPlaceholder);
                    }
                });
            }
        }

        // حفظ حالة الوضع الليلي قبل مغادرة الصفحة
        window.addEventListener('beforeunload', function() {
            const isDarkMode = document.body.style.filter.includes('invert');
            localStorage.setItem('darkMode', isDarkMode ? 'true' : 'false');
            {% if user_language == 'en' %}
                console.log('💾 Saving dark mode state before leaving:', isDarkMode ? 'enabled' : 'disabled');
            {% else %}
                console.log('💾 حفظ حالة الوضع الليلي قبل المغادرة:', isDarkMode ? 'مفعل' : 'معطل');
            {% endif %}
        });






    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
