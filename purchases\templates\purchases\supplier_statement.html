{% extends 'base.html' %}
{% load static %}

{% block title %}كشف حساب المورد - {{ supplier.name }}{% endblock %}

{% block extra_css %}
<style>
    .statement-header {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(231, 76, 60, 0.3);
    }

    .statement-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border-left: 5px solid #e74c3c;
    }

    .balance-summary {
        background: linear-gradient(135deg, #8e44ad, #9b59b6);
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        text-align: center;
    }

    .balance-amount {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
    }

    .transaction-item {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 10px;
        transition: all 0.3s ease;
    }

    .transaction-item:hover {
        background: #e9ecef;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .transaction-date {
        font-weight: 600;
        color: #2c3e50;
    }

    .transaction-amount {
        font-weight: 700;
        font-size: 1.1rem;
    }

    .amount-debit { color: #e74c3c; }
    .amount-credit { color: #27ae60; }

    .table-statement {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .table-statement thead th {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
        color: white;
        border: none;
        padding: 15px;
        font-weight: 600;
    }

    .table-statement tbody td {
        padding: 12px 15px;
        vertical-align: middle;
        border-bottom: 1px solid #e9ecef;
    }

    .btn-print {
        background: linear-gradient(135deg, #3498db, #2980b9);
        border: none;
        color: white;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-print:hover {
        background: linear-gradient(135deg, #2980b9, #1f4e79);
        transform: translateY(-2px);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- رأس كشف الحساب -->
    <div class="statement-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-3">
                    <i class="bi bi-building-fill"></i>
                    كشف حساب المورد
                </h1>
                <h3>{{ supplier.name }}</h3>
                <p class="mb-0">
                    <i class="bi bi-telephone"></i> {{ supplier.phone|default:"غير محدد" }} |
                    <i class="bi bi-envelope"></i> {{ supplier.email|default:"غير محدد" }}
                </p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-print" onclick="window.print()">
                    <i class="bi bi-printer"></i>
                    طباعة كشف الحساب
                </button>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- ملخص الرصيد -->
        <div class="col-md-4">
            <div class="balance-summary">
                <h4>الرصيد الحالي</h4>
                <div class="balance-amount">{{ balance|floatformat:2 }} ج.م</div>
                <p class="mb-0">
                    {% if balance > 0 %}
                        مستحق علينا
                    {% elif balance < 0 %}
                        مدفوع زيادة
                    {% else %}
                        متوازن
                    {% endif %}
                </p>
            </div>

            <div class="statement-card">
                <h6>معلومات المورد</h6>
                <div class="mb-2">
                    <strong>الرقم الضريبي:</strong> {{ supplier.tax_number|default:"غير محدد" }}
                </div>
                <div class="mb-2">
                    <strong>شروط الدفع:</strong> {{ supplier.payment_terms|default:"30 يوم" }}
                </div>
                <div class="mb-2">
                    <strong>العنوان:</strong> {{ supplier.address|default:"غير محدد" }}
                </div>
                <div class="mb-2">
                    <strong>الحالة:</strong>
                    {% if supplier.is_active %}
                        <span class="badge bg-success">نشط</span>
                    {% else %}
                        <span class="badge bg-danger">غير نشط</span>
                    {% endif %}
                </div>
            </div>

            <div class="statement-card">
                <h6>إحصائيات سريعة</h6>
                <div class="mb-2">
                    <strong>إجمالي الفواتير:</strong> {{ total_invoices|floatformat:2 }} ج.م
                </div>
                <div class="mb-2">
                    <strong>إجمالي المدفوعات:</strong> {{ total_payments|floatformat:2 }} ج.م
                </div>
                <div class="mb-2">
                    <strong>عدد الفواتير:</strong> {{ invoices.count }}
                </div>
                <div class="mb-2">
                    <strong>عدد المدفوعات:</strong> {{ payments|length }}
                </div>
            </div>
        </div>

        <!-- تفاصيل الحساب -->
        <div class="col-md-8">
            <!-- الفواتير -->
            <div class="statement-card">
                <h5 class="mb-4">
                    <i class="bi bi-receipt"></i>
                    فواتير الشراء
                </h5>

                <div class="table-responsive">
                    <table class="table table-statement">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>التاريخ</th>
                                <th>الاستحقاق</th>
                                <th>المبلغ</th>
                                <th>المدفوع</th>
                                <th>المتبقي</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in invoices %}
                            <tr>
                                <td>
                                    <a href="{% url 'purchases:purchase_invoice_detail' invoice.pk %}" class="text-decoration-none">
                                        {{ invoice.invoice_number }}
                                    </a>
                                </td>
                                <td>{{ invoice.invoice_date|date:"d/m/Y" }}</td>
                                <td>
                                    {% if invoice.due_date %}
                                        {{ invoice.due_date|date:"d/m/Y" }}
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td class="amount-debit">{{ invoice.total_amount|floatformat:2 }} ج.م</td>
                                <td class="amount-credit">{{ invoice.paid_amount|floatformat:2 }} ج.م</td>
                                <td>{{ invoice.remaining_amount|floatformat:2 }} ج.م</td>
                                <td>
                                    {% if invoice.remaining_amount <= 0 %}
                                        <span class="badge bg-success">مدفوعة</span>
                                    {% elif invoice.paid_amount > 0 %}
                                        <span class="badge bg-warning">جزئية</span>
                                    {% elif invoice.is_overdue %}
                                        <span class="badge bg-danger">متأخرة</span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير مدفوعة</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="7" class="text-center text-muted">لا توجد فواتير</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- المدفوعات -->
            <div class="statement-card">
                <h5 class="mb-4">
                    <i class="bi bi-cash-stack"></i>
                    المدفوعات للمورد
                </h5>

                <div class="table-responsive">
                    <table class="table table-statement">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                                <th>رقم المرجع</th>
                                <th>ملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in payments %}
                            <tr>
                                <td>{{ payment.payment_date|date:"d/m/Y" }}</td>
                                <td class="amount-credit">{{ payment.total_amount|floatformat:2 }} ج.م</td>
                                <td>{{ payment.get_payment_method_display }}</td>
                                <td>{{ payment.reference_number|default:"-" }}</td>
                                <td>{{ payment.notes|default:"-" }}</td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center text-muted">لا توجد مدفوعات</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار الإجراءات -->
    <div class="text-center mt-4 mb-4">
        <a href="{% url 'purchases:supplier_detail' supplier.pk %}" class="btn btn-primary btn-lg me-3">
            <i class="bi bi-building"></i>
            تفاصيل المورد
        </a>
        {% if balance > 0 %}
        <button class="btn btn-success btn-lg me-3" data-bs-toggle="modal" data-bs-target="#paymentModal">
            <i class="bi bi-credit-card"></i>
            دفع المستحق ({{ balance|floatformat:2 }} ج.م)
        </button>
        {% endif %}
        <a href="{% url 'accounts_receivable_payable' %}" class="btn btn-outline-secondary btn-lg">
            <i class="bi bi-arrow-left"></i>
            العودة للمديونيات
        </a>
    </div>
</div>

<!-- Modal للدفع -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="bi bi-credit-card"></i>
                    دفع للمورد: {{ supplier.name }}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form method="post" action="{% url 'purchases:supplier_payment_simple' supplier.pk %}" id="paymentForm">
                    {% csrf_token %}

                    <div class="alert alert-warning">
                        <h6><i class="bi bi-exclamation-triangle"></i> إجمالي المبلغ المستحق</h6>
                        <h4 class="mb-0 text-danger">{{ balance|floatformat:2 }} ج.م</h4>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">المبلغ المراد دفعه <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="number" name="amount" id="paymentAmount" class="form-control"
                                   step="0.01" min="1" max="{{ balance }}" placeholder="أدخل المبلغ المراد دفعه" required>
                            <span class="input-group-text">ج.م</span>
                        </div>
                        <div class="form-text">
                            <i class="bi bi-info-circle"></i>
                            يمكنك دفع أي مبلغ من 1 ج.م إلى {{ balance|floatformat:2 }} ج.م
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-4">
                            <button type="button" class="btn btn-outline-primary btn-sm w-100"
                                    onclick="setAmount({{ balance }})">
                                كامل المبلغ
                            </button>
                        </div>
                        <div class="col-4">
                            <button type="button" class="btn btn-outline-secondary btn-sm w-100"
                                    onclick="setAmount({{ balance|floatformat:0 }}/2)">
                                نصف المبلغ
                            </button>
                        </div>
                        <div class="col-4">
                            <button type="button" class="btn btn-outline-info btn-sm w-100"
                                    onclick="setAmount(10000)">
                                10,000 ج.م
                            </button>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">طريقة الدفع <span class="text-danger">*</span></label>
                        <select name="payment_method" id="paymentMethod" class="form-select" required>
                            <option value="">اختر طريقة الدفع</option>
                            <option value="cash" selected>نقدي</option>
                            <option value="bank_transfer">تحويل بنكي</option>
                            <option value="check">شيك</option>
                            <option value="credit_card">بطاقة ائتمان</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea name="notes" id="paymentNotes" class="form-control" rows="3"
                                  placeholder="ملاحظات إضافية حول الدفعة"></textarea>
                    </div>

                    <div class="text-end">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="bi bi-x-circle"></i>
                            إلغاء
                        </button>
                        <button type="submit" class="btn btn-success" id="submitPayment">
                            <i class="bi bi-check-circle"></i>
                            تأكيد الدفع
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// دالة لتعيين المبلغ
function setAmount(amount) {
    // إذا كان المبلغ عبارة عن تعبير رياضي، احسبه
    if (typeof amount === 'string' && amount.includes('/')) {
        amount = eval(amount);
    }
    document.getElementById('paymentAmount').value = Math.round(amount * 100) / 100;
    document.getElementById('paymentAmount').focus();
}

document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('paymentForm');
    const submitBtn = document.getElementById('submitPayment');
    const amountInput = document.getElementById('paymentAmount');

    // التركيز على حقل المبلغ عند فتح المودال
    document.getElementById('paymentModal').addEventListener('shown.bs.modal', function() {
        amountInput.focus();
        amountInput.select();
    });

    if (form && submitBtn) {
        form.addEventListener('submit', function(e) {
            const amount = parseFloat(amountInput.value);
            const maxAmount = {{ balance }};

            console.log('🔍 تم إرسال النموذج');
            console.log('🔍 المبلغ:', amount);
            console.log('🔍 الحد الأقصى:', maxAmount);

            // التحقق من المبلغ
            if (!amount || amount <= 0) {
                e.preventDefault();
                alert('❌ يرجى إدخال مبلغ صحيح');
                amountInput.focus();
                return false;
            }

            if (amount > maxAmount) {
                e.preventDefault();
                alert('❌ المبلغ أكبر من المستحق (' + maxAmount + ' ج.م)');
                amountInput.focus();
                return false;
            }

            // تأكيد الدفع
            if (!confirm('هل أنت متأكد من دفع ' + amount + ' ج.م؟')) {
                e.preventDefault();
                return false;
            }

            // تغيير نص الزر
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> جاري المعالجة...';
            submitBtn.disabled = true;

            console.log('✅ تم تأكيد الدفع');
        });
    }
});
</script>
{% endblock %}

