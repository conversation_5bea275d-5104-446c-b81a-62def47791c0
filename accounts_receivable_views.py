from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Sum, Count, Q, F
from django.http import JsonResponse
from datetime import datetime, timedelta, date
from django.utils import timezone

# استيراد النماذج
from sales.models import Customer, SalesInvoice, Payment as SalesPayment
from purchases.models import Supplier, PurchaseInvoice, SupplierPayment, SupplierAccount


@login_required
def accounts_receivable_payable(request):
    """صفحة إدارة المديونيات الشاملة"""
    
    # فلاتر البحث
    search = request.GET.get('search', '')
    account_type = request.GET.get('type', '')
    status_filter = request.GET.get('status', '')
    sort_by = request.GET.get('sort', 'name')
    
    today = timezone.now().date()
    
    # ========== الحسابات المدينة (العملاء) ==========
    customers = Customer.objects.filter(is_active=True)
    if search:
        customers = customers.filter(name__icontains=search)
    
    customers_data = []
    total_receivable = 0
    overdue_receivable = 0
    
    for customer in customers:
        # حساب إجمالي الفواتير غير المدفوعة
        unpaid_invoices = SalesInvoice.objects.filter(
            customer=customer,
            status__in=['sent', 'overdue']
        )
        
        customer_debt = 0
        last_invoice_date = None
        days_overdue = 0
        
        for invoice in unpaid_invoices:
            # للبساطة، نعتبر جميع الفواتير غير المدفوعة كمديونيات
            customer_debt += invoice.total_amount
            
            if invoice.invoice_date:
                if not last_invoice_date or invoice.invoice_date > last_invoice_date:
                    last_invoice_date = invoice.invoice_date
            
            # حساب أيام التأخير
            if invoice.due_date and invoice.due_date < today:
                overdue_days = (today - invoice.due_date).days
                if overdue_days > days_overdue:
                    days_overdue = overdue_days
        
        if customer_debt > 0:  # فقط العملاء الذين لديهم مديونيات
            customer_data = {
                'customer': customer,
                'total_debt': customer_debt,
                'last_invoice_date': last_invoice_date,
                'days_overdue': days_overdue,
                'invoices_count': unpaid_invoices.count(),
            }
            
            # تطبيق فلتر الحالة
            if status_filter == 'current' and days_overdue > 0:
                continue
            elif status_filter == 'overdue' and days_overdue == 0:
                continue
            elif status_filter == 'critical' and days_overdue <= 30:
                continue
            
            customers_data.append(customer_data)
            total_receivable += customer_debt
            
            if days_overdue > 0:
                overdue_receivable += customer_debt
    
    # ========== الحسابات الدائنة (الموردين) ==========
    suppliers = Supplier.objects.filter(is_active=True)
    if search:
        suppliers = suppliers.filter(name__icontains=search)
    
    suppliers_data = []
    total_payable = 0
    overdue_payable = 0
    
    for supplier in suppliers:
        # حساب إجمالي الفواتير غير المدفوعة
        unpaid_invoices = PurchaseInvoice.objects.filter(
            supplier=supplier
        )
        
        supplier_debt = 0
        last_invoice_date = None
        days_overdue = 0
        
        for invoice in unpaid_invoices:
            # حساب المتبقي يدوياً
            paid_amount = invoice.paid_amount or 0
            remaining = invoice.total_amount - paid_amount
            if remaining > 0:
                supplier_debt += remaining
                
                if invoice.invoice_date:
                    if not last_invoice_date or invoice.invoice_date > last_invoice_date:
                        last_invoice_date = invoice.invoice_date
                
                # حساب أيام التأخير
                if invoice.due_date and invoice.due_date < today:
                    overdue_days = (today - invoice.due_date).days
                    if overdue_days > days_overdue:
                        days_overdue = overdue_days
        
        if supplier_debt > 0:  # فقط الموردين الذين لديهم مديونيات
            supplier_data = {
                'supplier': supplier,
                'total_debt': supplier_debt,
                'last_invoice_date': last_invoice_date,
                'days_overdue': days_overdue,
                'invoices_count': unpaid_invoices.count(),
            }
            
            # تطبيق فلتر الحالة
            if status_filter == 'current' and days_overdue > 0:
                continue
            elif status_filter == 'overdue' and days_overdue == 0:
                continue
            elif status_filter == 'critical' and days_overdue <= 30:
                continue
            
            suppliers_data.append(supplier_data)
            total_payable += supplier_debt
            
            if days_overdue > 0:
                overdue_payable += supplier_debt
    
    # ========== تطبيق الفلاتر ==========
    if account_type == 'customer':
        suppliers_data = []
    elif account_type == 'supplier':
        customers_data = []
    
    # ========== ترتيب النتائج ==========
    if sort_by == 'amount':
        customers_data.sort(key=lambda x: x['total_debt'], reverse=True)
        suppliers_data.sort(key=lambda x: x['total_debt'], reverse=True)
    elif sort_by == 'overdue':
        customers_data.sort(key=lambda x: x['days_overdue'], reverse=True)
        suppliers_data.sort(key=lambda x: x['days_overdue'], reverse=True)
    else:
        customers_data.sort(key=lambda x: x['customer'].name)
        suppliers_data.sort(key=lambda x: x['supplier'].name)
    
    # ========== الحسابات الحرجة ==========
    critical_debts = []
    critical_amount = 0
    
    for customer_data in customers_data:
        if customer_data['days_overdue'] > 30:
            critical_debts.append({
                'type': 'customer',
                'name': customer_data['customer'].name,
                'amount': customer_data['total_debt'],
                'days_overdue': customer_data['days_overdue']
            })
            critical_amount += customer_data['total_debt']
    
    for supplier_data in suppliers_data:
        if supplier_data['days_overdue'] > 30:
            critical_debts.append({
                'type': 'supplier',
                'name': supplier_data['supplier'].name,
                'amount': supplier_data['total_debt'],
                'days_overdue': supplier_data['days_overdue']
            })
            critical_amount += supplier_data['total_debt']
    
    # ========== السياق ==========
    context = {
        'customers_data': customers_data,
        'suppliers_data': suppliers_data,
        'customers_count': len(customers_data),
        'suppliers_count': len(suppliers_data),
        'total_clients': len(customers_data) + len(suppliers_data),
        'total_receivable': total_receivable,
        'total_payable': total_payable,
        'overdue_receivable': overdue_receivable,
        'overdue_payable': overdue_payable,
        'critical_debts': critical_debts,
        'critical_amount': critical_amount,
        'today': today,
    }
    
    return render(request, 'accounts_receivable_payable.html', context)


@login_required
def customer_statement(request, customer_id):
    """كشف حساب العميل"""
    customer = get_object_or_404(Customer, pk=customer_id)
    
    # جلب جميع الفواتير والمدفوعات
    invoices = SalesInvoice.objects.filter(customer=customer).order_by('-invoice_date')
    payments = SalesPayment.objects.filter(customer=customer).order_by('-payment_date')
    
    # حساب الرصيد
    total_invoices = invoices.aggregate(Sum('total_amount'))['total_amount__sum'] or 0
    total_payments = payments.aggregate(Sum('amount'))['amount__sum'] or 0
    balance = total_invoices - total_payments
    
    context = {
        'customer': customer,
        'invoices': invoices,
        'payments': payments,
        'total_invoices': total_invoices,
        'total_payments': total_payments,
        'balance': balance,
    }
    
    return render(request, 'sales/customer_statement.html', context)


@login_required
def supplier_statement(request, supplier_id):
    """كشف حساب المورد"""
    supplier = get_object_or_404(Supplier, pk=supplier_id)
    
    # جلب جميع الفواتير والمدفوعات
    invoices = PurchaseInvoice.objects.filter(supplier=supplier).order_by('-invoice_date')
    
    # جلب المدفوعات من خلال التخصيصات
    payments = []
    for invoice in invoices:
        for allocation in invoice.payment_allocations.all():
            payments.append(allocation.payment)
    
    # إزالة المدفوعات المكررة وترتيبها
    payments = list(set(payments))
    payments.sort(key=lambda x: x.payment_date, reverse=True)
    
    # حساب الرصيد
    total_invoices = invoices.aggregate(Sum('total_amount'))['total_amount__sum'] or 0
    total_payments = sum(payment.total_amount for payment in payments)
    balance = total_invoices - total_payments
    
    context = {
        'supplier': supplier,
        'invoices': invoices,
        'payments': payments,
        'total_invoices': total_invoices,
        'total_payments': total_payments,
        'balance': balance,
    }
    
    return render(request, 'purchases/supplier_statement.html', context)


@login_required
def collect_from_customer(request, customer_id):
    """تحصيل من العميل"""
    customer = get_object_or_404(Customer, pk=customer_id)
    
    if request.method == 'POST':
        try:
            amount = float(request.POST.get('amount', 0))
            payment_method = request.POST.get('payment_method', 'cash')
            notes = request.POST.get('notes', '')
            
            if amount <= 0:
                messages.error(request, '❌ يرجى إدخال مبلغ صحيح')
                return redirect('accounts_receivable_payable')
            
            # إنشاء دفعة تحصيل
            payment = SalesPayment.objects.create(
                customer=customer,
                amount=amount,
                payment_method=payment_method,
                payment_type='collection',
                payment_date=timezone.now().date(),
                notes=notes,
                created_by=request.user
            )
            
            messages.success(request, f'✅ تم تحصيل {amount} ج.م من {customer.name} بنجاح!')
            
        except Exception as e:
            messages.error(request, f'❌ خطأ في التحصيل: {str(e)}')
    
    return redirect('accounts_receivable_payable')


@login_required
def pay_to_supplier(request, supplier_id):
    """دفع للمورد"""
    supplier = get_object_or_404(Supplier, pk=supplier_id)
    
    if request.method == 'POST':
        try:
            amount = float(request.POST.get('amount', 0))
            payment_method = request.POST.get('payment_method', 'cash')
            notes = request.POST.get('notes', '')
            
            if amount <= 0:
                messages.error(request, '❌ يرجى إدخال مبلغ صحيح')
                return redirect('accounts_receivable_payable')
            
            # إنشاء دفعة للمورد
            payment = SupplierPayment.objects.create(
                supplier=supplier,
                total_amount=amount,
                payment_method=payment_method,
                payment_date=timezone.now().date(),
                notes=notes,
                created_by=request.user
            )
            
            messages.success(request, f'✅ تم دفع {amount} ج.م لـ {supplier.name} بنجاح!')
            
        except Exception as e:
            messages.error(request, f'❌ خطأ في الدفع: {str(e)}')
    
    return redirect('accounts_receivable_payable')


@login_required
def debt_analytics(request):
    """تحليلات المديونيات"""
    today = timezone.now().date()
    
    # تحليل العملاء
    customers_analysis = []
    for customer in Customer.objects.filter(is_active=True):
        unpaid_invoices = SalesInvoice.objects.filter(
            customer=customer,
            status__in=['sent', 'overdue']
        )
        
        total_debt = sum(
            invoice.total_amount - (invoice.paid_amount or 0) 
            for invoice in unpaid_invoices
        )
        
        if total_debt > 0:
            customers_analysis.append({
                'customer': customer,
                'debt': total_debt,
                'credit_utilization': (total_debt / customer.credit_limit * 100) if customer.credit_limit > 0 else 0,
                'risk_level': 'high' if total_debt > customer.credit_limit else 'medium' if total_debt > customer.credit_limit * 0.8 else 'low'
            })
    
    # تحليل الموردين
    suppliers_analysis = []
    for supplier in Supplier.objects.filter(is_active=True):
        unpaid_invoices = PurchaseInvoice.objects.filter(supplier=supplier)
        total_debt = 0
        for invoice in unpaid_invoices:
            paid_amount = invoice.paid_amount or 0
            remaining = invoice.total_amount - paid_amount
            if remaining > 0:
                total_debt += remaining
        
        if total_debt > 0:
            suppliers_analysis.append({
                'supplier': supplier,
                'debt': total_debt,
                'overdue_count': unpaid_invoices.filter(due_date__lt=today).count()
            })
    
    context = {
        'customers_analysis': customers_analysis,
        'suppliers_analysis': suppliers_analysis,
    }
    
    return render(request, 'debt_analytics.html', context)
