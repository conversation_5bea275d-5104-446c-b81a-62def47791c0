{% extends 'base.html' %}

{% block title %}{% if invoice %}تعديل فاتورة الشراء{% else %}إنشاء فاتورة شراء جديدة{% endif %} - نظام أوساريك{% endblock %}

{% block extra_css %}
    <style>
        .page-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 40px 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .form-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 25px;
            color: #333;
            border-bottom: 3px solid #dc3545;
            padding-bottom: 10px;
        }
        
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #dc3545, #c82333);
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(220, 53, 69, 0.3);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #6c757d, #5a6268);
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(108, 117, 125, 0.3);
        }
        
        .items-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
        }
        
        .item-row {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .item-row:hover {
            border-color: #dc3545;
            transform: translateY(-2px);
        }
        
        .btn-add-item {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-add-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
            color: white;
        }
        
        .btn-remove-item {
            background: linear-gradient(45deg, #dc3545, #c82333);
            border: none;
            color: white;
            padding: 8px 15px;
            border-radius: 15px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-remove-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
            color: white;
        }
        
        .total-section {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .total-row:last-child {
            margin-bottom: 0;
            font-size: 1.2rem;
            font-weight: 700;
            border-top: 2px solid rgba(255, 255, 255, 0.3);
            padding-top: 15px;
            margin-top: 15px;
        }
        
        .required {
            color: #dc3545;
        }
        
        .alert {
            border-radius: 15px;
            border: none;
            padding: 15px 20px;
        }
        
        .alert-success {
            background: linear-gradient(45deg, #d4edda, #c3e6cb);
            color: #155724;
        }
        
        .alert-danger {
            background: linear-gradient(45deg, #f8d7da, #f5c6cb);
            color: #721c24;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="container-fluid py-4">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="bi bi-receipt"></i>
                        {% if invoice %}تعديل فاتورة الشراء{% else %}إنشاء فاتورة شراء جديدة{% endif %}
                    </h1>
                    <p class="mb-0">{% if invoice %}تعديل بيانات فاتورة الشراء رقم {{ invoice.invoice_number }}{% else %}إضافة فاتورة شراء جديدة للنظام{% endif %}</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{% url 'purchases:purchase_invoice_list' %}" class="btn btn-outline-light">
                        <i class="bi bi-arrow-left"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>

        <!-- عرض الرسائل -->
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}

        <!-- نموذج الفاتورة الاحترافي -->
        <form method="post" action="" id="invoiceForm">
            {% csrf_token %}

            <!-- بيانات الفاتورة الأساسية -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-file-earmark-text"></i>
                    بيانات الفاتورة الأساسية
                </h3>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">المورد <span class="required">*</span></label>
                            <select name="supplier" class="form-select" required>
                                <option value="">اختر المورد</option>
                                {% for supplier in suppliers %}
                                    <option value="{{ supplier.id }}" {% if invoice and invoice.supplier.id == supplier.id %}selected{% endif %}>
                                        {{ supplier.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">رقم الفاتورة</label>
                            <input type="text" name="invoice_number" class="form-control"
                                   value="{% if invoice %}{{ invoice.invoice_number }}{% else %}سيتم إنشاؤه تلقائياً{% endif %}"
                                   placeholder="سيتم إنشاؤه تلقائياً" readonly style="background: #f8f9fa;">
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">تاريخ الفاتورة <span class="required">*</span></label>
                            <input type="date" name="invoice_date" class="form-control" 
                                   value="{% if invoice %}{{ invoice.invoice_date|date:'Y-m-d' }}{% else %}{{ today|date:'Y-m-d' }}{% endif %}" 
                                   required>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">تاريخ الاستحقاق</label>
                            <input type="date" name="due_date" class="form-control" 
                                   value="{% if invoice %}{{ invoice.due_date|date:'Y-m-d' }}{% endif %}">
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">حالة الفاتورة</label>
                            <select name="status" class="form-select">
                                <option value="received" {% if invoice and invoice.status == 'received' %}selected{% endif %}>مستلمة</option>
                                <option value="partially_paid" {% if invoice and invoice.status == 'partially_paid' %}selected{% endif %}>مدفوعة جزئياً</option>
                                <option value="paid" {% if invoice and invoice.status == 'paid' %}selected{% endif %}>مدفوعة بالكامل</option>
                                <option value="overdue" {% if invoice and invoice.status == 'overdue' %}selected{% endif %}>متأخرة</option>
                                <option value="cancelled" {% if invoice and invoice.status == 'cancelled' %}selected{% endif %}>ملغية</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea name="notes" class="form-control" rows="3"
                                      placeholder="ملاحظات إضافية">{% if invoice %}{{ invoice.notes }}{% endif %}</textarea>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات الدفع -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-cash-stack"></i>
                    معلومات الدفع
                </h3>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">إجمالي الفاتورة <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="totalAmountInput" name="total_amount"
                                       placeholder="0.00" step="0.01" min="0" required onchange="calculateRemaining()">
                                <span class="input-group-text">ج.م</span>
                            </div>
                            <small class="text-muted">يرجى إدخال إجمالي الفاتورة يدوياً</small>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">المبلغ المدفوع</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="paidAmount" name="paid_amount_manual"
                                       placeholder="0.00" step="0.01" min="0" value="0" onchange="calculateRemaining()">
                                <span class="input-group-text">ج.م</span>
                            </div>
                            <small class="text-muted">المبلغ المدفوع مع هذه الفاتورة</small>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">المبلغ المتبقي</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="remainingAmountDisplay" readonly
                                       placeholder="سيتم حسابه تلقائياً" style="background: #f8f9fa;">
                                <span class="input-group-text">ج.م</span>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">طريقة الدفع</label>
                            <select class="form-select" name="payment_method">
                                <option value="">لم يتم الدفع بعد</option>
                                <option value="cash">نقدي</option>
                                <option value="bank_transfer">تحويل بنكي</option>
                                <option value="check">شيك</option>
                                <option value="credit_card">بطاقة ائتمان</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">رقم المرجع</label>
                            <input type="text" class="form-control" name="payment_reference"
                                   placeholder="رقم الشيك، رقم التحويل، إلخ">
                        </div>
                    </div>
                </div>
            </div>

            <!-- أصناف الفاتورة -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-list-ul"></i>
                    أصناف الفاتورة
                </h3>

                <div class="items-section" id="itemsSection">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5>الأصناف</h5>
                        <button type="button" class="btn btn-add-item" onclick="addItem()">
                            <i class="bi bi-plus-circle"></i>
                            إضافة صنف
                        </button>
                    </div>



                    <!-- رأس الجدول -->
                    <div class="row mb-2" style="background: #f8f9fa; padding: 10px; border-radius: 5px;">
                        <div class="col-md-3"><strong>المنتج</strong></div>
                        <div class="col-md-2"><strong>المخزن</strong></div>
                        <div class="col-md-1"><strong>الكمية</strong></div>
                        <div class="col-md-2"><strong>سعر الوحدة</strong></div>
                        <div class="col-md-1"><strong>الخصم (%)</strong></div>
                        <div class="col-md-2"><strong>الإجمالي</strong></div>
                        <div class="col-md-1"><strong>حذف</strong></div>
                    </div>

                    <div id="itemsList">
                        <!-- سيتم إضافة الأصناف هنا بواسطة JavaScript -->
                    </div>

                    <!-- قالب مخفي للأصناف -->
                    <div id="itemTemplate" style="display: none;">
                        <div class="item-row">
                            <div class="row">
                                <div class="col-md-3">
                                    <label class="form-label">المنتج</label>
                                    <select name="product_COUNTER" class="form-select product-select">
                                        <option value="">اختر المنتج</option>
                                        {% for product in products %}
                                            <option value="{{ product.id }}" data-price="{{ product.cost_price|default:0 }}">
                                                {{ product.name }}{% if product.code %} ({{ product.code }}){% endif %}
                                            </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">المخزن <span class="text-danger">*</span></label>
                                    <select name="warehouse_COUNTER" class="form-select warehouse-select" required>
                                        <option value="">اختر المخزن</option>
                                        {% for warehouse in warehouses %}
                                            <option value="{{ warehouse.id }}">
                                                {{ warehouse.name }} ({{ warehouse.code }}) - {{ warehouse.get_warehouse_type_display }}
                                            </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-1">
                                    <label class="form-label">الكمية</label>
                                    <input type="number" name="quantity_COUNTER" class="form-control quantity-input"
                                           min="1" step="0.01" placeholder="1">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">سعر الوحدة</label>
                                    <input type="number" name="unit_price_COUNTER" class="form-control price-input"
                                           min="0" step="0.01" placeholder="0.00">
                                </div>
                                <div class="col-md-1">
                                    <label class="form-label">الخصم (%)</label>
                                    <input type="number" name="discount_COUNTER" class="form-control discount-input"
                                           min="0" max="100" step="0.01" value="0">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">الإجمالي</label>
                                    <input type="text" class="form-control item-total" readonly style="background: #f8f9fa;">
                                </div>
                                <div class="col-md-1 d-flex align-items-end">
                                    <button type="button" class="btn btn-remove-item remove-btn">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إجمالي الفاتورة -->
                <div class="total-section">
                    <div class="alert alert-info mb-3">
                        <i class="bi bi-info-circle"></i>
                        <strong>ملاحظة:</strong> الحسابات التالية للمرجعية فقط. يرجى إدخال إجمالي الفاتورة الفعلي في قسم الدفع أعلاه.
                    </div>
                    <div class="total-row">
                        <span>المجموع الفرعي:</span>
                        <span id="subtotal">0.00 ج.م</span>
                    </div>
                    <div class="total-row">
                        <span>الخصم:</span>
                        <span id="discount">0.00 ج.م</span>
                    </div>
                    <div class="total-row">
                        <span>الضريبة:</span>
                        <span id="tax">0.00 ج.م</span>
                    </div>
                    <div class="total-row">
                        <span>الإجمالي المحسوب (مرجعي):</span>
                        <span id="total">0.00 ج.م</span>
                    </div>
                </div>
            </div>

            <!-- زر حفظ بسيط -->
            <div class="text-center mt-4 mb-4">
                <button type="submit" class="btn btn-success btn-lg">
                    حفظ الفاتورة
                </button>
                <a href="{% url 'purchases:purchase_invoice_list' %}" class="btn btn-outline-secondary btn-lg ms-3">
                    العودة
                </a>
            </div>
        </form>
    </div>
{% endblock %}

{% block extra_js %}
    <script>
        let itemCounter = 0;

        // إضافة صنف جديد
        function addItem() {
            itemCounter++;

            // استنساخ القالب المخفي
            const template = document.getElementById('itemTemplate');
            const newItem = template.cloneNode(true);

            // إزالة الـ id وإظهار العنصر
            newItem.removeAttribute('id');
            newItem.style.display = 'block';
            newItem.id = `item_${itemCounter}`;

            // تحديث أسماء الحقول
            const html = newItem.innerHTML.replace(/COUNTER/g, itemCounter);
            newItem.innerHTML = html;

            // إضافة event listeners
            const productSelect = newItem.querySelector('.product-select');
            const quantityInput = newItem.querySelector('.quantity-input');
            const priceInput = newItem.querySelector('.price-input');
            const discountInput = newItem.querySelector('.discount-input');
            const removeBtn = newItem.querySelector('.remove-btn');

            productSelect.addEventListener('change', function() {
                updateItemPrice(itemCounter);
            });

            quantityInput.addEventListener('change', function() {
                calculateItemTotal(itemCounter);
            });

            priceInput.addEventListener('change', function() {
                calculateItemTotal(itemCounter);
            });

            discountInput.addEventListener('change', function() {
                calculateItemTotal(itemCounter);
            });

            removeBtn.addEventListener('click', function() {
                removeItem(itemCounter);
            });

            // إضافة العنصر للقائمة
            document.getElementById('itemsList').appendChild(newItem);
        }

        // حذف صنف
        function removeItem(itemId) {
            document.getElementById(`item_${itemId}`).remove();
            calculateTotal();
        }

        // تحديث سعر المنتج عند الاختيار
        function updateItemPrice(itemId) {
            const productSelect = document.querySelector(`select[name="product_${itemId}"]`);
            const priceInput = document.querySelector(`input[name="unit_price_${itemId}"]`);

            if (productSelect && productSelect.selectedIndex > 0) {
                const selectedOption = productSelect.options[productSelect.selectedIndex];
                const price = selectedOption.getAttribute('data-price') || 0;
                if (priceInput) {
                    priceInput.value = price;
                    calculateItemTotal(itemId);
                }
            }
        }

        // حساب إجمالي الصنف
        function calculateItemTotal(itemId) {
            const quantityInput = document.querySelector(`input[name="quantity_${itemId}"]`);
            const priceInput = document.querySelector(`input[name="unit_price_${itemId}"]`);
            const discountInput = document.querySelector(`input[name="discount_${itemId}"]`);
            const totalInput = document.querySelector(`#item_${itemId} .item-total`);

            if (quantityInput && priceInput && discountInput && totalInput) {
                const quantity = parseFloat(quantityInput.value) || 0;
                const unitPrice = parseFloat(priceInput.value) || 0;
                const discount = parseFloat(discountInput.value) || 0;

                const subtotal = quantity * unitPrice;
                const discountAmount = subtotal * (discount / 100);
                const total = subtotal - discountAmount;

                totalInput.value = total.toFixed(2);
                calculateTotal();
            }
        }

        // حساب الإجمالي العام
        function calculateTotal() {
            let subtotal = 0;
            let totalDiscount = 0;

            // جمع إجماليات جميع الأصناف
            document.querySelectorAll('.item-total').forEach(input => {
                subtotal += parseFloat(input.value) || 0;
            });

            // حساب الضريبة (يمكن تخصيصها)
            const taxRate = 0; // 0% ضريبة افتراضياً
            const tax = subtotal * (taxRate / 100);
            const total = subtotal + tax;

            // تحديث العرض
            const subtotalElement = document.getElementById('subtotal');
            const discountElement = document.getElementById('discount');
            const taxElement = document.getElementById('tax');
            const totalElement = document.getElementById('total');

            if (subtotalElement) subtotalElement.textContent = subtotal.toFixed(2) + ' ج.م';
            if (discountElement) discountElement.textContent = totalDiscount.toFixed(2) + ' ج.م';
            if (taxElement) taxElement.textContent = tax.toFixed(2) + ' ج.م';
            if (totalElement) totalElement.textContent = total.toFixed(2) + ' ج.م';
        }

        // حساب المبلغ المتبقي
        function calculateRemaining() {
            const totalAmount = parseFloat(document.getElementById('totalAmountInput').value) || 0;
            const paidAmount = parseFloat(document.getElementById('paidAmount').value) || 0;
            const remaining = totalAmount - paidAmount;

            document.getElementById('remainingAmountDisplay').value = remaining.toFixed(2);

            // تغيير لون النص حسب الحالة
            const remainingInput = document.getElementById('remainingAmountDisplay');
            if (remaining === 0) {
                remainingInput.style.color = '#28a745'; // أخضر للمدفوع بالكامل
                remainingInput.style.fontWeight = 'bold';
            } else if (remaining > 0) {
                remainingInput.style.color = '#dc3545'; // أحمر لغير المدفوع
                remainingInput.style.fontWeight = 'bold';
            } else {
                remainingInput.style.color = '#ffc107'; // أصفر للدفع الزائد
                remainingInput.style.fontWeight = 'bold';
            }
        }

        // إضافة صنف افتراضي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addItem();
        });
    </script>
{% endblock %}
