from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from django.utils import timezone
from decimal import Decimal
from datetime import timedelta

# استيراد نماذج المخازن والمنتجات من التطبيقات الموجودة
from definitions.models import WarehouseDefinition, ProductDefinition
from warehouses.models import InventoryItem, InventoryTransaction


# إزالة نماذج المخازن والمنتجات المحلية واستخدام النماذج الموجودة
# class Warehouse - تم استبدالها بـ WarehouseDefinition
# class Product - تم استبدالها بـ ProductDefinition
# class Stock - تم استبدالها بـ InventoryItem
# class StockMovement - تم استبدالها بـ InventoryTransaction


class Warehouse(models.Model):
    """نموذج المخازن"""
    name = models.CharField(max_length=100, verbose_name="اسم المخزن")
    code = models.CharField(max_length=20, unique=True, verbose_name="كود المخزن")
    location = models.CharField(max_length=200, verbose_name="الموقع")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    manager = models.CharField(max_length=100, blank=True, null=True, verbose_name="مدير المخزن")
    phone = models.CharField(max_length=20, blank=True, null=True, verbose_name="الهاتف")
    capacity = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True, verbose_name="السعة (متر مكعب)")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "مخزن"
        verbose_name_plural = "المخازن"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"

    @property
    def total_products(self):
        """إجمالي المنتجات في المخزن"""
        return self.stock_items.count()

    @property
    def total_value(self):
        """إجمالي قيمة المخزون"""
        total = Decimal('0')
        for item in self.stock_items.all():
            if item.product.purchase_price:
                total += item.quantity * item.product.purchase_price
        return total


class Supplier(models.Model):
    """نموذج الموردين"""
    name = models.CharField(max_length=200, verbose_name="اسم المورد")
    email = models.EmailField(blank=True, null=True, verbose_name="البريد الإلكتروني")
    phone = models.CharField(max_length=20, blank=True, null=True, verbose_name="رقم الهاتف")
    address = models.TextField(blank=True, null=True, verbose_name="العنوان")
    tax_number = models.CharField(max_length=50, blank=True, null=True, verbose_name="الرقم الضريبي")
    payment_terms = models.CharField(max_length=100, blank=True, null=True, verbose_name="شروط الدفع")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "مورد"
        verbose_name_plural = "الموردين"
        ordering = ['name']

    def __str__(self):
        return self.name

class Product(models.Model):
    """نموذج المنتجات للمشتريات"""
    name = models.CharField(max_length=200, verbose_name="اسم المنتج")
    code = models.CharField(max_length=50, unique=True, verbose_name="كود المنتج")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    purchase_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="سعر الشراء")
    selling_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="سعر البيع")
    stock_quantity = models.IntegerField(default=0, verbose_name="الكمية المتاحة")
    min_stock_level = models.IntegerField(default=0, verbose_name="الحد الأدنى للمخزون")
    unit = models.CharField(max_length=20, default="قطعة", verbose_name="الوحدة")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "منتج"
        verbose_name_plural = "المنتجات"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"

    @property
    def needs_reorder(self):
        return self.stock_quantity <= self.min_stock_level

class PurchaseOrder(models.Model):
    """نموذج أوامر الشراء"""
    STATUS_CHOICES = [
        ('draft', 'مسودة'),
        ('sent', 'مرسل'),
        ('confirmed', 'مؤكد'),
        ('received', 'تم الاستلام'),
        ('cancelled', 'ملغي'),
    ]

    order_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الطلب")
    supplier = models.ForeignKey(Supplier, on_delete=models.CASCADE, verbose_name="المورد")
    order_date = models.DateField(verbose_name="تاريخ الطلب")
    expected_delivery_date = models.DateField(blank=True, null=True, verbose_name="تاريخ التسليم المتوقع")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name="الحالة")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="نسبة الخصم")
    tax_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=15, verbose_name="نسبة الضريبة")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "أمر شراء"
        verbose_name_plural = "أوامر الشراء"
        ordering = ['-created_at']

    def __str__(self):
        return f"أمر شراء {self.order_number} - {self.supplier.name}"

    @property
    def subtotal(self):
        return sum(item.total_price for item in self.items.all())

    @property
    def discount_amount(self):
        return self.subtotal * (self.discount_percentage / 100)

    @property
    def tax_amount(self):
        return (self.subtotal - self.discount_amount) * (self.tax_percentage / 100)

    @property
    def total_amount(self):
        return self.subtotal - self.discount_amount + self.tax_amount

class PurchaseOrderItem(models.Model):
    """نموذج عناصر أمر الشراء"""
    order = models.ForeignKey(PurchaseOrder, related_name='items', on_delete=models.CASCADE, verbose_name="أمر الشراء")
    product = models.ForeignKey(ProductDefinition, on_delete=models.CASCADE, verbose_name="المنتج")
    warehouse = models.ForeignKey(WarehouseDefinition, on_delete=models.CASCADE, blank=True, null=True, verbose_name="المخزن المستهدف")
    quantity = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0.01)], verbose_name="الكمية")
    unit_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="سعر الوحدة")
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="نسبة الخصم")
    received_quantity = models.DecimalField(max_digits=10, decimal_places=3, default=0, verbose_name="الكمية المستلمة")
    is_received = models.BooleanField(default=False, verbose_name="تم الاستلام")

    class Meta:
        verbose_name = "عنصر أمر شراء"
        verbose_name_plural = "عناصر أوامر الشراء"

    def __str__(self):
        return f"{self.product.name} - {self.quantity} {self.product.unit}"

    @property
    def total_price(self):
        subtotal = self.quantity * self.unit_price
        discount = subtotal * (self.discount_percentage / 100)
        return subtotal - discount

class PurchaseInvoice(models.Model):
    """نموذج فواتير الشراء"""
    STATUS_CHOICES = [
        ('draft', 'مسودة'),
        ('received', 'مستلمة'),
        ('partially_paid', 'مدفوعة جزئياً'),
        ('paid', 'مدفوعة بالكامل'),
        ('overdue', 'متأخرة'),
        ('cancelled', 'ملغية'),
    ]

    invoice_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الفاتورة")
    supplier = models.ForeignKey(Supplier, on_delete=models.CASCADE, verbose_name="المورد")
    order = models.ForeignKey(PurchaseOrder, blank=True, null=True, on_delete=models.SET_NULL, verbose_name="أمر الشراء")
    invoice_date = models.DateField(verbose_name="تاريخ الفاتورة")
    due_date = models.DateField(verbose_name="تاريخ الاستحقاق")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name="الحالة")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="نسبة الخصم")
    tax_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=15, verbose_name="نسبة الضريبة")
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="إجمالي الفاتورة")
    paid_amount_manual = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="المبلغ المدفوع")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "فاتورة شراء"
        verbose_name_plural = "فواتير الشراء"
        ordering = ['-created_at']

    def __str__(self):
        return f"فاتورة {self.invoice_number} - {self.supplier.name}"

    @property
    def subtotal(self):
        return sum(item.total_price for item in self.items.all())

    @property
    def discount_amount(self):
        from decimal import Decimal
        return self.subtotal * (self.discount_percentage / Decimal('100'))

    @property
    def tax_amount(self):
        from decimal import Decimal
        return (self.subtotal - self.discount_amount) * (self.tax_percentage / Decimal('100'))

    @property
    def calculated_total_amount(self):
        """الإجمالي المحسوب تلقائياً (للمرجعية فقط)"""
        return self.subtotal - self.discount_amount + self.tax_amount

    @property
    def paid_amount(self):
        """إجمالي المبلغ المدفوع (يدوي + تخصيصات)"""
        allocated = sum(allocation.allocated_amount for allocation in self.payment_allocations.all())
        manual = self.paid_amount_manual or 0
        return allocated + manual

    @property
    def remaining_amount(self):
        """المبلغ المتبقي"""
        return max(0, self.total_amount - self.paid_amount)

    @property
    def payment_percentage(self):
        """نسبة السداد"""
        if self.total_amount > 0:
            return (self.paid_amount / self.total_amount) * 100
        return 0

    @property
    def get_payment_status_class(self):
        """فئة CSS لحالة الدفع"""
        if self.remaining_amount <= 0:
            return 'paid'
        elif self.paid_amount > 0:
            return 'partial'
        elif self.due_date and self.due_date < timezone.now().date():
            return 'overdue'
        else:
            return 'unpaid'

    @property
    def is_overdue(self):
        """هل الفاتورة متأخرة؟"""
        return (self.due_date and
                self.due_date < timezone.now().date() and
                self.remaining_amount > 0)

    def update_payment_status(self):
        """تحديث حالة الدفع تلقائياً"""
        paid = self.paid_amount
        total = self.total_amount
        old_status = self.status

        if paid == 0:
            if self.due_date and self.due_date < timezone.now().date():
                self.status = 'overdue'
            elif self.status not in ['draft', 'cancelled']:
                self.status = 'received'
        elif paid >= total:
            self.status = 'paid'
        else:
            self.status = 'partially_paid'

        # حفظ فقط إذا تغيرت الحالة لتجنب recursion
        if old_status != self.status:
            super().save(update_fields=['status'])

    def save(self, *args, **kwargs):
        """حفظ بسيط بدون تعقيدات"""
        super().save(*args, **kwargs)

class PurchaseInvoiceItem(models.Model):
    """نموذج عناصر فاتورة الشراء"""
    invoice = models.ForeignKey(PurchaseInvoice, related_name='items', on_delete=models.CASCADE, verbose_name="فاتورة الشراء")
    product = models.ForeignKey(ProductDefinition, on_delete=models.CASCADE, verbose_name="المنتج")
    warehouse = models.ForeignKey(WarehouseDefinition, on_delete=models.CASCADE, blank=True, null=True, verbose_name="المخزن المستهدف")
    quantity = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0.01)], verbose_name="الكمية")
    unit_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="سعر الوحدة")
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="نسبة الخصم")
    applied_to_stock = models.BooleanField(default=False, verbose_name="تم التطبيق على المخزون")
    applied_at = models.DateTimeField(blank=True, null=True, verbose_name="تاريخ التطبيق")
    applied_by = models.ForeignKey(User, blank=True, null=True, on_delete=models.SET_NULL, verbose_name="طبق بواسطة")

    class Meta:
        verbose_name = "عنصر فاتورة شراء"
        verbose_name_plural = "عناصر فواتير الشراء"

    def __str__(self):
        return f"{self.product.name} - {self.quantity} {self.product.unit}"

    @property
    def total_price(self):
        subtotal = self.quantity * self.unit_price
        discount = subtotal * (self.discount_percentage / 100)
        return subtotal - discount

    def apply_to_stock(self, user):
        """تطبيق الفاتورة على المخزون"""
        if not self.applied_to_stock and self.warehouse:
            from django.utils import timezone

            try:
                # البحث عن عنصر المخزون أو إنشاؤه
                inventory_item, created = InventoryItem.objects.get_or_create(
                    warehouse=self.warehouse,
                    product=self.product,
                    defaults={
                        'quantity_on_hand': 0,
                        'average_cost': self.unit_price,
                        'last_cost': self.unit_price,
                        'is_active': True
                    }
                )

                # تحديث الكمية والتكلفة باستخدام المتوسط المرجح
                old_quantity = inventory_item.quantity_on_hand
                old_value = old_quantity * inventory_item.average_cost
                new_value = self.quantity * self.unit_price
                total_quantity = old_quantity + self.quantity

                if total_quantity > 0:
                    inventory_item.average_cost = (old_value + new_value) / total_quantity

                inventory_item.quantity_on_hand += self.quantity
                inventory_item.last_cost = self.unit_price
                inventory_item.total_value = inventory_item.quantity_on_hand * inventory_item.average_cost
                inventory_item.last_received_date = timezone.now()
                inventory_item.save()

                # تسجيل حركة المخزون
                from datetime import datetime

                # إنشاء رقم حركة فريد
                transaction_number = f"PUR-{self.invoice.invoice_number}-{self.id}"

                InventoryTransaction.objects.create(
                    transaction_number=transaction_number,
                    inventory_item=inventory_item,
                    transaction_type='receipt',
                    transaction_date=timezone.now(),
                    quantity=self.quantity,
                    unit_cost=self.unit_price,
                    total_cost=self.quantity * self.unit_price,
                    balance_before=old_quantity,
                    balance_after=total_quantity,
                    reference_number=self.invoice.invoice_number,
                    notes=f"استلام من فاتورة شراء {self.invoice.invoice_number}",
                    created_by=user
                )

                # تحديث حالة التطبيق
                self.applied_to_stock = True
                self.applied_at = timezone.now()
                self.applied_by = user
                self.save()

                return True

            except Exception as e:
                print(f"خطأ في تطبيق المخزون: {str(e)}")
                return False

        return False


# تم نقل نماذج المخزون إلى نظام المخازن الرئيسي
# Stock -> InventoryItem
# StockMovement -> InventoryTransaction


class SupplierPayment(models.Model):
    """نموذج مدفوعات الموردين المطور"""
    PAYMENT_METHOD_CHOICES = [
        ('cash', 'نقدي'),
        ('bank_transfer', 'تحويل بنكي'),
        ('check', 'شيك'),
        ('credit_card', 'بطاقة ائتمان'),
        ('other', 'أخرى'),
    ]

    # معلومات أساسية
    supplier = models.ForeignKey(Supplier, on_delete=models.CASCADE, related_name='all_payments', verbose_name="المورد", default=1)
    payment_date = models.DateField(verbose_name="تاريخ الدفع")
    total_amount = models.DecimalField(max_digits=12, decimal_places=2, verbose_name="إجمالي المبلغ المدفوع")
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES, verbose_name="طريقة الدفع")
    reference_number = models.CharField(max_length=100, blank=True, null=True, verbose_name="رقم المرجع")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")

    # معلومات التتبع
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "دفعة مورد"
        verbose_name_plural = "دفعات الموردين"
        ordering = ['-payment_date', '-created_at']

    def __str__(self):
        return f"{self.supplier.name} - {self.total_amount} ج.م - {self.payment_date}"

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        # تحديث حساب المورد
        account, created = SupplierAccount.objects.get_or_create(supplier=self.supplier)
        account.update_balance()


class PaymentAllocation(models.Model):
    """توزيع الدفعة على الفواتير - لإدارة المديونيات المعقدة"""
    payment = models.ForeignKey(SupplierPayment, on_delete=models.CASCADE, related_name='allocations', verbose_name="الدفعة")
    invoice = models.ForeignKey(PurchaseInvoice, on_delete=models.CASCADE, related_name='payment_allocations', verbose_name="الفاتورة")
    allocated_amount = models.DecimalField(max_digits=12, decimal_places=2, verbose_name="المبلغ المخصص")
    allocation_date = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ التخصيص")

    class Meta:
        verbose_name = "تخصيص دفعة"
        verbose_name_plural = "تخصيصات الدفعات"
        unique_together = ['payment', 'invoice']

    def __str__(self):
        return f"تخصيص {self.allocated_amount} ج.م من دفعة {self.payment.id} للفاتورة {self.invoice.invoice_number}"

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        # تحديث حالة الفاتورة بعد التخصيص
        self.invoice.update_payment_status()

        # إنشاء معاملة في سجل المورد
        SupplierTransaction.objects.create(
            supplier=self.invoice.supplier,
            transaction_type='payment',
            transaction_date=self.payment.payment_date,
            reference_number=f"PAY-{self.payment.id}",
            description=f"دفعة مخصصة للفاتورة {self.invoice.invoice_number}",
            credit_amount=self.allocated_amount,
            debit_amount=0,
            balance_after=0,  # سيتم تحديثه لاحقاً
            invoice=self.invoice,
            payment=self.payment,
            created_by=self.payment.created_by
        )


class SupplierAccount(models.Model):
    """حساب المورد الجاري - لإدارة المديونيات المعقدة"""
    supplier = models.OneToOneField(Supplier, on_delete=models.CASCADE, related_name='account', verbose_name="المورد")
    current_balance = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="الرصيد الحالي")
    credit_limit = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="حد الائتمان")
    last_transaction_date = models.DateTimeField(blank=True, null=True, verbose_name="تاريخ آخر معاملة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "حساب مورد"
        verbose_name_plural = "حسابات الموردين"

    def __str__(self):
        return f"حساب {self.supplier.name} - رصيد: {self.current_balance} ج.م"

    @property
    def available_credit(self):
        """الائتمان المتاح"""
        return self.credit_limit - self.current_balance

    @property
    def is_over_limit(self):
        """هل تجاوز حد الائتمان"""
        return self.current_balance > self.credit_limit

    def update_balance(self):
        """تحديث الرصيد من جميع المعاملات"""
        total_invoices = sum(
            invoice.total_amount for invoice in self.supplier.purchaseinvoice_set.exclude(status='cancelled')
        )
        total_payments = sum(
            payment.amount for payment in SupplierPayment.objects.filter(invoice__supplier=self.supplier)
        )
        self.current_balance = total_invoices - total_payments
        self.last_transaction_date = timezone.now()
        self.save()


class SupplierTransaction(models.Model):
    """معاملات المورد - سجل شامل لجميع المعاملات المالية"""
    TRANSACTION_TYPE_CHOICES = [
        ('invoice', 'فاتورة شراء'),
        ('payment', 'دفعة'),
        ('credit_note', 'إشعار دائن'),
        ('debit_note', 'إشعار مدين'),
        ('adjustment', 'تسوية'),
        ('opening_balance', 'رصيد افتتاحي'),
    ]

    supplier = models.ForeignKey(Supplier, on_delete=models.CASCADE, related_name='transactions', verbose_name="المورد")
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPE_CHOICES, verbose_name="نوع المعاملة")
    transaction_date = models.DateTimeField(verbose_name="تاريخ المعاملة")
    reference_number = models.CharField(max_length=100, verbose_name="رقم المرجع")
    description = models.TextField(verbose_name="الوصف")

    # المبالغ
    debit_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="مدين")
    credit_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="دائن")
    balance_after = models.DecimalField(max_digits=15, decimal_places=2, verbose_name="الرصيد بعد المعاملة")

    # المراجع
    invoice = models.ForeignKey(PurchaseInvoice, blank=True, null=True, on_delete=models.CASCADE, verbose_name="فاتورة الشراء")
    payment = models.ForeignKey(SupplierPayment, blank=True, null=True, on_delete=models.CASCADE, verbose_name="الدفعة")

    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "معاملة مورد"
        verbose_name_plural = "معاملات الموردين"
        ordering = ['-transaction_date', '-created_at']

    def __str__(self):
        return f"{self.supplier.name} - {self.get_transaction_type_display()} - {self.transaction_date.date()}"

    @property
    def net_amount(self):
        """صافي المبلغ (مدين - دائن)"""
        return self.debit_amount - self.credit_amount


class PaymentAlert(models.Model):
    """نظام التنبيهات للمستحقات والمتأخرات"""
    ALERT_TYPE_CHOICES = [
        ('due_soon', 'مستحقة قريباً'),
        ('overdue', 'متأخرة'),
        ('payment_reminder', 'تذكير دفع'),
        ('credit_limit', 'تجاوز حد الائتمان'),
        ('large_payment', 'دفعة كبيرة تحتاج موافقة'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'منخفضة'),
        ('medium', 'متوسطة'),
        ('high', 'عالية'),
        ('critical', 'حرجة'),
    ]

    alert_type = models.CharField(max_length=20, choices=ALERT_TYPE_CHOICES, verbose_name="نوع التنبيه")
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='medium', verbose_name="الأولوية")
    title = models.CharField(max_length=200, verbose_name="عنوان التنبيه")
    message = models.TextField(verbose_name="رسالة التنبيه")

    # المراجع
    supplier = models.ForeignKey(Supplier, blank=True, null=True, on_delete=models.CASCADE, verbose_name="المورد")
    invoice = models.ForeignKey(PurchaseInvoice, blank=True, null=True, on_delete=models.CASCADE, verbose_name="الفاتورة")
    payment = models.ForeignKey(SupplierPayment, blank=True, null=True, on_delete=models.CASCADE, verbose_name="الدفعة")

    # معلومات التنبيه
    amount = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True, verbose_name="المبلغ")
    due_date = models.DateField(blank=True, null=True, verbose_name="تاريخ الاستحقاق")

    # حالة التنبيه
    is_read = models.BooleanField(default=False, verbose_name="مقروء")
    is_resolved = models.BooleanField(default=False, verbose_name="تم الحل")
    resolved_at = models.DateTimeField(blank=True, null=True, verbose_name="تاريخ الحل")
    resolved_by = models.ForeignKey(User, blank=True, null=True, on_delete=models.SET_NULL,
                                   related_name='resolved_alerts', verbose_name="حُل بواسطة")

    # معلومات الإنشاء
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_alerts', verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "تنبيه دفع"
        verbose_name_plural = "تنبيهات الدفع"
        ordering = ['-priority', '-created_at']

    def __str__(self):
        return f"{self.get_priority_display()} - {self.title}"

    def mark_as_read(self, user=None):
        """تحديد التنبيه كمقروء"""
        self.is_read = True
        self.save()

    def resolve(self, user=None):
        """حل التنبيه"""
        self.is_resolved = True
        self.resolved_at = timezone.now()
        if user:
            self.resolved_by = user
        self.save()

    @property
    def days_overdue(self):
        """عدد أيام التأخير"""
        if self.due_date and self.due_date < timezone.now().date():
            return (timezone.now().date() - self.due_date).days
        return 0

    @property
    def urgency_score(self):
        """درجة الإلحاح (للترتيب)"""
        priority_scores = {'low': 1, 'medium': 2, 'high': 3, 'critical': 4}
        base_score = priority_scores.get(self.priority, 2)

        # زيادة الدرجة حسب أيام التأخير
        if self.days_overdue > 0:
            base_score += min(self.days_overdue / 10, 2)  # حد أقصى +2

        return base_score


class PaymentApproval(models.Model):
    """نظام الموافقات للمدفوعات الكبيرة"""
    STATUS_CHOICES = [
        ('pending', 'في انتظار الموافقة'),
        ('approved', 'موافق عليها'),
        ('rejected', 'مرفوضة'),
        ('cancelled', 'ملغية'),
    ]

    payment = models.OneToOneField(SupplierPayment, on_delete=models.CASCADE, related_name='approval', verbose_name="الدفعة")
    amount_threshold = models.DecimalField(max_digits=12, decimal_places=2, verbose_name="حد المبلغ المطلوب للموافقة")

    # معلومات الطلب
    requested_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='payment_requests', verbose_name="طلب بواسطة")
    requested_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الطلب")
    justification = models.TextField(verbose_name="مبرر الدفعة")

    # معلومات الموافقة
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name="حالة الموافقة")
    approved_by = models.ForeignKey(User, blank=True, null=True, on_delete=models.SET_NULL,
                                   related_name='approved_payments', verbose_name="وافق بواسطة")
    approved_at = models.DateTimeField(blank=True, null=True, verbose_name="تاريخ الموافقة")
    rejection_reason = models.TextField(blank=True, null=True, verbose_name="سبب الرفض")

    # معلومات إضافية
    priority = models.CharField(max_length=10, choices=PaymentAlert.PRIORITY_CHOICES, default='medium', verbose_name="الأولوية")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات إضافية")

    class Meta:
        verbose_name = "موافقة دفعة"
        verbose_name_plural = "موافقات الدفعات"
        ordering = ['-requested_at']

    def __str__(self):
        return f"موافقة دفعة {self.payment.total_amount} ج.م - {self.get_status_display()}"

    def approve(self, user, notes=None):
        """الموافقة على الدفعة"""
        self.status = 'approved'
        self.approved_by = user
        self.approved_at = timezone.now()
        if notes:
            self.notes = notes
        self.save()

        # إنشاء تنبيه بالموافقة
        PaymentAlert.objects.create(
            alert_type='payment_reminder',
            priority='medium',
            title=f'تم الموافقة على دفعة {self.payment.total_amount} ج.م',
            message=f'تم الموافقة على دفعة للمورد {self.payment.supplier.name}',
            supplier=self.payment.supplier,
            payment=self.payment,
            amount=self.payment.total_amount,
            created_by=user
        )

    def reject(self, user, reason):
        """رفض الدفعة"""
        self.status = 'rejected'
        self.approved_by = user
        self.approved_at = timezone.now()
        self.rejection_reason = reason
        self.save()

        # إنشاء تنبيه بالرفض
        PaymentAlert.objects.create(
            alert_type='payment_reminder',
            priority='high',
            title=f'تم رفض دفعة {self.payment.total_amount} ج.م',
            message=f'تم رفض دفعة للمورد {self.payment.supplier.name}. السبب: {reason}',
            supplier=self.payment.supplier,
            payment=self.payment,
            amount=self.payment.total_amount,
            created_by=user
        )

    @property
    def is_pending(self):
        return self.status == 'pending'

    @property
    def days_pending(self):
        """عدد أيام انتظار الموافقة"""
        return (timezone.now() - self.requested_at).days


class SupplierRiskAssessment(models.Model):
    """تحليل المخاطر وتقييم الموردين"""
    RISK_LEVEL_CHOICES = [
        ('low', 'منخفضة'),
        ('medium', 'متوسطة'),
        ('high', 'عالية'),
        ('critical', 'حرجة'),
    ]

    supplier = models.OneToOneField(Supplier, on_delete=models.CASCADE, related_name='risk_assessment', verbose_name="المورد")

    # مؤشرات المخاطر المالية
    payment_history_score = models.IntegerField(default=0, verbose_name="درجة تاريخ الدفع")  # 0-100
    credit_utilization = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="نسبة استخدام الائتمان")
    average_payment_delay = models.IntegerField(default=0, verbose_name="متوسط تأخير الدفع (أيام)")

    # مؤشرات الأداء
    order_fulfillment_rate = models.DecimalField(max_digits=5, decimal_places=2, default=100, verbose_name="معدل تنفيذ الطلبات")
    quality_score = models.IntegerField(default=100, verbose_name="درجة الجودة")  # 0-100
    delivery_performance = models.IntegerField(default=100, verbose_name="أداء التسليم")  # 0-100

    # التقييم العام
    overall_risk_level = models.CharField(max_length=10, choices=RISK_LEVEL_CHOICES, default='medium', verbose_name="مستوى المخاطر العام")
    risk_score = models.IntegerField(default=50, verbose_name="درجة المخاطر")  # 0-100 (أعلى = أكثر خطورة)

    # التوصيات
    recommended_credit_limit = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="حد الائتمان المقترح")
    payment_terms_recommendation = models.CharField(max_length=100, blank=True, null=True, verbose_name="شروط الدفع المقترحة")

    # معلومات التحديث
    last_assessment_date = models.DateTimeField(auto_now=True, verbose_name="تاريخ آخر تقييم")
    assessed_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="قُيم بواسطة")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات التقييم")

    class Meta:
        verbose_name = "تقييم مخاطر مورد"
        verbose_name_plural = "تقييمات مخاطر الموردين"

    def __str__(self):
        return f"تقييم مخاطر {self.supplier.name} - {self.get_overall_risk_level_display()}"

    def calculate_risk_score(self):
        """حساب درجة المخاطر تلقائياً"""
        score = 0

        # تاريخ الدفع (30% من التقييم)
        payment_risk = max(0, 100 - self.payment_history_score)
        score += payment_risk * 0.3

        # استخدام الائتمان (25% من التقييم)
        credit_risk = min(100, float(self.credit_utilization))
        score += credit_risk * 0.25

        # تأخير الدفع (25% من التقييم)
        delay_risk = min(100, self.average_payment_delay * 2)  # كل يوم تأخير = 2 نقطة
        score += delay_risk * 0.25

        # الأداء العام (20% من التقييم)
        performance_risk = 100 - ((self.order_fulfillment_rate + self.quality_score + self.delivery_performance) / 3)
        score += float(performance_risk) * 0.2

        self.risk_score = min(100, max(0, int(score)))

        # تحديد مستوى المخاطر
        if self.risk_score <= 25:
            self.overall_risk_level = 'low'
        elif self.risk_score <= 50:
            self.overall_risk_level = 'medium'
        elif self.risk_score <= 75:
            self.overall_risk_level = 'high'
        else:
            self.overall_risk_level = 'critical'

        self.save()

    def update_payment_history(self):
        """تحديث تاريخ الدفع من البيانات الفعلية"""
        invoices = PurchaseInvoice.objects.filter(supplier=self.supplier)
        if not invoices.exists():
            return

        total_invoices = invoices.count()
        paid_on_time = 0
        total_delay_days = 0

        for invoice in invoices:
            if invoice.status == 'paid':
                # حساب تأخير الدفع
                last_payment = invoice.payment_allocations.order_by('-allocation_date').first()
                if last_payment:
                    payment_date = last_payment.allocation_date.date()
                    if payment_date <= invoice.due_date:
                        paid_on_time += 1
                    else:
                        delay_days = (payment_date - invoice.due_date).days
                        total_delay_days += delay_days

        # حساب درجة تاريخ الدفع
        if total_invoices > 0:
            on_time_rate = (paid_on_time / total_invoices) * 100
            self.payment_history_score = int(on_time_rate)

            # حساب متوسط التأخير
            delayed_invoices = total_invoices - paid_on_time
            if delayed_invoices > 0:
                self.average_payment_delay = total_delay_days // delayed_invoices
            else:
                self.average_payment_delay = 0

        self.save()

    @property
    def risk_color(self):
        """لون المخاطر للعرض"""
        colors = {
            'low': '#28a745',
            'medium': '#ffc107',
            'high': '#fd7e14',
            'critical': '#dc3545'
        }
        return colors.get(self.overall_risk_level, '#6c757d')


class CashFlowForecast(models.Model):
    """توقعات التدفق النقدي"""
    PERIOD_CHOICES = [
        ('weekly', 'أسبوعي'),
        ('monthly', 'شهري'),
        ('quarterly', 'ربع سنوي'),
    ]

    period_type = models.CharField(max_length=20, choices=PERIOD_CHOICES, verbose_name="نوع الفترة")
    start_date = models.DateField(verbose_name="تاريخ البداية")
    end_date = models.DateField(verbose_name="تاريخ النهاية")

    # التوقعات المالية
    expected_purchases = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="المشتريات المتوقعة")
    expected_payments = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="المدفوعات المتوقعة")
    outstanding_invoices = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="الفواتير المعلقة")

    # التحليل
    cash_flow_impact = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="تأثير التدفق النقدي")
    confidence_level = models.IntegerField(default=80, verbose_name="مستوى الثقة %")

    # معلومات الإنشاء
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "توقع التدفق النقدي"
        verbose_name_plural = "توقعات التدفق النقدي"
        ordering = ['-start_date']

    def __str__(self):
        return f"توقع التدفق النقدي {self.start_date} - {self.end_date}"

    def calculate_forecast(self):
        """حساب التوقعات تلقائياً"""
        # حساب الفواتير المستحقة في الفترة
        due_invoices = PurchaseInvoice.objects.filter(
            due_date__range=[self.start_date, self.end_date],
            status__in=['received', 'partially_paid', 'overdue']
        )

        self.outstanding_invoices = sum(invoice.remaining_amount for invoice in due_invoices)

        # تقدير المشتريات الجديدة (بناءً على المتوسط التاريخي)
        historical_period = (self.end_date - self.start_date).days
        historical_start = self.start_date - timedelta(days=historical_period * 3)  # آخر 3 فترات مماثلة

        historical_purchases = PurchaseInvoice.objects.filter(
            invoice_date__range=[historical_start, self.start_date]
        ).aggregate(
            total=models.Sum('total_amount')
        )['total'] or 0

        # متوسط المشتريات لكل فترة
        periods_count = 3
        self.expected_purchases = historical_purchases / periods_count if periods_count > 0 else 0

        # تقدير المدفوعات (بناءً على نمط الدفع التاريخي)
        self.expected_payments = self.outstanding_invoices * 0.7  # افتراض دفع 70% من المستحقات

        # حساب تأثير التدفق النقدي
        self.cash_flow_impact = self.expected_payments - self.expected_purchases

        self.save()

    @property
    def is_positive_flow(self):
        """هل التدفق النقدي إيجابي"""
        return self.cash_flow_impact >= 0

    @property
    def flow_status(self):
        """حالة التدفق النقدي"""
        if self.cash_flow_impact > 0:
            return 'positive'
        elif self.cash_flow_impact < 0:
            return 'negative'
        else:
            return 'neutral'


class BankIntegration(models.Model):
    """ربط مع البنوك لتسجيل المدفوعات تلقائياً"""
    BANK_CHOICES = [
        ('nbe', 'البنك الأهلي المصري'),
        ('cib', 'البنك التجاري الدولي'),
        ('banque_misr', 'بنك مصر'),
        ('hsbc', 'بنك HSBC'),
        ('other', 'بنك آخر'),
    ]

    bank_name = models.CharField(max_length=50, choices=BANK_CHOICES, verbose_name="اسم البنك")
    account_number = models.CharField(max_length=50, verbose_name="رقم الحساب")
    account_name = models.CharField(max_length=100, verbose_name="اسم الحساب")

    # معلومات الاتصال (محاكاة)
    api_endpoint = models.URLField(blank=True, null=True, verbose_name="نقطة الاتصال API")
    api_key = models.CharField(max_length=200, blank=True, null=True, verbose_name="مفتاح API")
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    # إعدادات المزامنة
    auto_sync = models.BooleanField(default=False, verbose_name="مزامنة تلقائية")
    last_sync = models.DateTimeField(blank=True, null=True, verbose_name="آخر مزامنة")
    sync_frequency = models.IntegerField(default=60, verbose_name="تكرار المزامنة (دقائق)")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "ربط بنكي"
        verbose_name_plural = "الربط البنكي"

    def __str__(self):
        return f"{self.get_bank_name_display()} - {self.account_number}"

    def sync_transactions(self):
        """مزامنة المعاملات من البنك (محاكاة)"""
        # هذه دالة محاكاة - في التطبيق الحقيقي ستتصل بـ API البنك

        # محاكاة استلام معاملات من البنك
        mock_transactions = [
            {
                'reference': 'TXN123456',
                'amount': 5000.00,
                'date': timezone.now().date(),
                'description': 'تحويل من مورد ABC',
                'type': 'credit'
            }
        ]

        processed_count = 0
        for transaction in mock_transactions:
            # البحث عن دفعة مطابقة
            matching_payment = SupplierPayment.objects.filter(
                total_amount=transaction['amount'],
                payment_date=transaction['date'],
                payment_method='bank_transfer'
            ).first()

            if matching_payment and not matching_payment.reference_number:
                matching_payment.reference_number = transaction['reference']
                matching_payment.save()
                processed_count += 1

        self.last_sync = timezone.now()
        self.save()

        return processed_count

    @property
    def needs_sync(self):
        """هل يحتاج لمزامنة"""
        if not self.auto_sync or not self.last_sync:
            return False

        next_sync = self.last_sync + timedelta(minutes=self.sync_frequency)
        return timezone.now() >= next_sync


# دوال مساعدة لإدارة التنبيهات والتقييمات
class AlertManager:
    """مدير التنبيهات"""

    @staticmethod
    def create_due_soon_alerts():
        """إنشاء تنبيهات للفواتير المستحقة قريباً"""
        from datetime import date, timedelta

        # الفواتير المستحقة خلال 7 أيام
        due_soon_date = date.today() + timedelta(days=7)
        due_invoices = PurchaseInvoice.objects.filter(
            due_date__lte=due_soon_date,
            due_date__gte=date.today(),
            status__in=['received', 'partially_paid']
        )

        for invoice in due_invoices:
            # تحقق من عدم وجود تنبيه مماثل
            existing_alert = PaymentAlert.objects.filter(
                alert_type='due_soon',
                invoice=invoice,
                is_resolved=False
            ).exists()

            if not existing_alert:
                days_until_due = (invoice.due_date - date.today()).days
                priority = 'high' if days_until_due <= 3 else 'medium'

                # الحصول على أول مستخدم متاح
                from django.contrib.auth.models import User
                default_user = User.objects.first()
                if default_user:
                    PaymentAlert.objects.create(
                        alert_type='due_soon',
                        priority=priority,
                        title=f'فاتورة مستحقة خلال {days_until_due} أيام',
                        message=f'فاتورة {invoice.invoice_number} للمورد {invoice.supplier.name} مستحقة في {invoice.due_date}',
                        supplier=invoice.supplier,
                        invoice=invoice,
                        amount=invoice.remaining_amount,
                        due_date=invoice.due_date,
                        created_by=default_user
                    )

    @staticmethod
    def create_overdue_alerts():
        """إنشاء تنبيهات للفواتير المتأخرة"""
        from datetime import date

        overdue_invoices = PurchaseInvoice.objects.filter(
            due_date__lt=date.today(),
            status__in=['received', 'partially_paid', 'overdue']
        )

        for invoice in overdue_invoices:
            # تحديث حالة الفاتورة
            if invoice.status != 'overdue':
                invoice.status = 'overdue'
                invoice.save()

            # تحقق من عدم وجود تنبيه حديث
            recent_alert = PaymentAlert.objects.filter(
                alert_type='overdue',
                invoice=invoice,
                created_at__gte=date.today()
            ).exists()

            if not recent_alert:
                days_overdue = (date.today() - invoice.due_date).days
                priority = 'critical' if days_overdue > 30 else 'high'

                # الحصول على أول مستخدم متاح
                from django.contrib.auth.models import User
                default_user = User.objects.first()
                if default_user:
                    PaymentAlert.objects.create(
                        alert_type='overdue',
                        priority=priority,
                        title=f'فاتورة متأخرة {days_overdue} يوم',
                        message=f'فاتورة {invoice.invoice_number} للمورد {invoice.supplier.name} متأخرة منذ {days_overdue} يوم',
                        supplier=invoice.supplier,
                        invoice=invoice,
                        amount=invoice.remaining_amount,
                        due_date=invoice.due_date,
                        created_by=default_user
                    )

    @staticmethod
    def check_credit_limits():
        """فحص تجاوز حدود الائتمان"""
        suppliers_with_accounts = Supplier.objects.filter(account__isnull=False)

        for supplier in suppliers_with_accounts:
            account = supplier.account
            if account.is_over_limit:
                # تحقق من عدم وجود تنبيه حديث
                recent_alert = PaymentAlert.objects.filter(
                    alert_type='credit_limit',
                    supplier=supplier,
                    created_at__gte=timezone.now().date()
                ).exists()

                if not recent_alert:
                    excess_amount = account.current_balance - account.credit_limit

                    # الحصول على أول مستخدم متاح
                    from django.contrib.auth.models import User
                    default_user = User.objects.first()
                    if default_user:
                        PaymentAlert.objects.create(
                            alert_type='credit_limit',
                            priority='high',
                            title=f'تجاوز حد الائتمان بمبلغ {excess_amount:.2f} ج.م',
                            message=f'المورد {supplier.name} تجاوز حد الائتمان المسموح ({account.credit_limit:.2f} ج.م)',
                            supplier=supplier,
                            amount=excess_amount,
                            created_by=default_user
                        )
