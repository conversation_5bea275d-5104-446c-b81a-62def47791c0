#!/usr/bin/env python
import os
import sys
import django

# إعداد Django
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric.settings')
django.setup()

from purchases.models import Supplier, SupplierPayment, PurchaseInvoice, PaymentAllocation
from django.db.models import Sum

def test_balance_calculation():
    """اختبار حساب الرصيد"""
    supplier = Supplier.objects.get(pk=4)
    print(f"المورد: {supplier.name}")
    
    # جلب البيانات
    invoices = PurchaseInvoice.objects.filter(supplier=supplier)
    payments = SupplierPayment.objects.filter(supplier=supplier)
    
    # حساب إجمالي الفواتير
    total_invoices = invoices.aggregate(Sum('total_amount'))['total_amount__sum'] or 0
    print(f"إجمالي الفواتير: {total_invoices}")
    
    # حساب المدفوعات من الفواتير (يدوي فقط)
    invoice_payments = invoices.aggregate(Sum('paid_amount_manual'))['paid_amount_manual__sum'] or 0
    print(f"مدفوعات يدوية: {invoice_payments}")
    
    # حساب التخصيصات من المدفوعات المنفصلة
    allocations = PaymentAllocation.objects.filter(invoice__supplier=supplier)
    allocated_payments = allocations.aggregate(Sum('allocated_amount'))['allocated_amount__sum'] or 0
    print(f"تخصيصات: {allocated_payments}")
    
    # حساب المدفوعات المنفصلة (إجمالي المدفوعات)
    separate_payments_total = payments.aggregate(Sum('total_amount'))['total_amount__sum'] or 0
    print(f"إجمالي المدفوعات المنفصلة: {separate_payments_total}")
    
    # المدفوعات المنفصلة غير المخصصة = إجمالي المدفوعات - التخصيصات
    separate_payments = separate_payments_total - allocated_payments
    print(f"مدفوعات منفصلة غير مخصصة: {separate_payments}")
    
    # إجمالي المدفوعات = مدفوعات يدوية + تخصيصات + مدفوعات منفصلة غير مخصصة
    total_payments = invoice_payments + allocated_payments + separate_payments
    print(f"إجمالي المدفوعات: {total_payments}")
    
    # الرصيد المتبقي
    balance = total_invoices - total_payments
    print(f"الرصيد المتبقي: {balance}")
    
    # حساب نصف المبلغ
    half_balance = balance / 2
    print(f"نصف المبلغ: {half_balance}")
    
    return balance, half_balance

if __name__ == "__main__":
    test_balance_calculation()
