from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Sum, Count, Q, F, Avg, Max, Min, Case, When, Value, IntegerField
from django.db import models
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator
from django.template.loader import get_template
from django.utils import timezone
from datetime import datetime, timedelta
import json
from .models import (Supplier, Product, PurchaseOrder, PurchaseOrderItem, PurchaseInvoice, PurchaseInvoiceItem, Warehouse,
                     SupplierPayment, PaymentAllocation, PaymentAlert, PaymentApproval,
                     SupplierRiskAssessment, CashFlowForecast, BankIntegration, AlertManager)
from definitions.models import WarehouseDefinition, ProductDefinition
from warehouses.models import InventoryItem, InventoryTransaction
from .forms import (SupplierForm, ProductForm, PurchaseOrderForm, PurchaseOrderItemFormSet,
                   PurchaseInvoiceForm, PurchaseInvoiceItemFormSet)

@login_required
def purchases_dashboard(request):
    """لوحة تحكم المشتريات"""
    # إحصائيات عامة
    total_suppliers = Supplier.objects.filter(is_active=True).count()
    total_products = Product.objects.filter(is_active=True).count()
    total_orders = PurchaseOrder.objects.count()
    total_invoices = PurchaseInvoice.objects.count()

    # إحصائيات مالية
    paid_invoices = PurchaseInvoice.objects.filter(status='paid')
    total_purchases = sum(invoice.total_amount for invoice in paid_invoices)
    pending_orders = PurchaseOrder.objects.filter(status__in=['sent', 'confirmed']).count()
    overdue_invoices = PurchaseInvoice.objects.filter(
        status='overdue', due_date__lt=datetime.now().date()).count()

    # المنتجات التي تحتاج إعادة طلب
    low_stock_products = Product.objects.filter(
        is_active=True,
        stock_quantity__lte=F('min_stock_level'),
        min_stock_level__isnull=False
    ).count()

    # أحدث الطلبات
    recent_orders = PurchaseOrder.objects.select_related('supplier').order_by('-created_at')[:5]

    # أحدث الفواتير
    recent_invoices = PurchaseInvoice.objects.select_related('supplier').order_by('-created_at')[:5]

    context = {
        'total_suppliers': total_suppliers,
        'total_products': total_products,
        'total_orders': total_orders,
        'total_invoices': total_invoices,
        'total_purchases': total_purchases,
        'pending_orders': pending_orders,
        'overdue_invoices': overdue_invoices,
        'low_stock_products': low_stock_products,
        'recent_orders': recent_orders,
        'recent_invoices': recent_invoices,
    }
    return render(request, 'purchases/dashboard.html', context)

# ========== إدارة الموردين ==========
@login_required
def supplier_list(request):
    """قائمة الموردين"""
    search = request.GET.get('search', '')
    suppliers = Supplier.objects.all()

    if search:
        suppliers = suppliers.filter(
            Q(name__icontains=search) |
            Q(email__icontains=search) |
            Q(phone__icontains=search)
        )

    # حساب الإحصائيات
    total_suppliers = Supplier.objects.count()
    active_suppliers = Supplier.objects.filter(is_active=True).count()
    inactive_suppliers = Supplier.objects.filter(is_active=False).count()

    # إحصائيات إضافية
    suppliers_with_email = Supplier.objects.exclude(email__isnull=True).exclude(email='').count()
    suppliers_with_phone = Supplier.objects.exclude(phone__isnull=True).exclude(phone='').count()

    paginator = Paginator(suppliers, 20)
    page_number = request.GET.get('page')
    suppliers = paginator.get_page(page_number)

    context = {
        'suppliers': suppliers,
        'search': search,
        'total_suppliers': total_suppliers,
        'active_suppliers': active_suppliers,
        'inactive_suppliers': inactive_suppliers,
        'suppliers_with_email': suppliers_with_email,
        'suppliers_with_phone': suppliers_with_phone,
    }
    return render(request, 'purchases/supplier_list.html', context)

@login_required
def supplier_create(request):
    """إضافة مورد جديد"""
    if request.method == 'POST':
        form = SupplierForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة المورد بنجاح')
            return redirect('purchases:supplier_list')
    else:
        form = SupplierForm()

    context = {'form': form, 'title': 'إضافة مورد جديد'}
    return render(request, 'purchases/supplier_form.html', context)

@login_required
def supplier_edit(request, pk):
    """تعديل مورد"""
    supplier = get_object_or_404(Supplier, pk=pk)
    if request.method == 'POST':
        form = SupplierForm(request.POST, instance=supplier)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث بيانات المورد بنجاح')
            return redirect('purchases:supplier_list')
    else:
        form = SupplierForm(instance=supplier)

    context = {'form': form, 'title': 'تعديل المورد', 'supplier': supplier}
    return render(request, 'purchases/supplier_form.html', context)

@login_required
def supplier_detail(request, pk):
    """تفاصيل المورد"""
    supplier = get_object_or_404(Supplier, pk=pk)

    # إحصائيات المورد
    total_orders = PurchaseOrder.objects.filter(supplier=supplier).count()
    total_invoices = PurchaseInvoice.objects.filter(supplier=supplier).count()

    # حساب إجمالي المشتريات باستخدام property
    invoices = PurchaseInvoice.objects.filter(supplier=supplier)
    total_purchases = sum(invoice.total_amount for invoice in invoices)

    # إحصائيات الدائن والمدين
    total_paid = sum(invoice.paid_amount for invoice in invoices)
    total_remaining = sum(invoice.remaining_amount for invoice in invoices)

    # إحصائيات حسب الحالة
    paid_invoices = invoices.filter(status='paid').count()
    partially_paid_invoices = invoices.filter(status='partially_paid').count()
    overdue_invoices = invoices.filter(status='overdue').count()
    other_invoices = total_invoices - paid_invoices - partially_paid_invoices - overdue_invoices

    # المبالغ المتأخرة
    overdue_amount = sum(
        invoice.remaining_amount for invoice in invoices.filter(status='overdue')
    )

    # أحدث الطلبات والفواتير
    recent_orders = PurchaseOrder.objects.filter(supplier=supplier).order_by('-created_at')[:5]
    recent_invoices = PurchaseInvoice.objects.filter(supplier=supplier).order_by('-created_at')[:5]

    context = {
        'supplier': supplier,
        'total_orders': total_orders,
        'total_invoices': total_invoices,
        'total_purchases': total_purchases,
        'total_paid': total_paid,
        'total_remaining': total_remaining,
        'paid_invoices': paid_invoices,
        'partially_paid_invoices': partially_paid_invoices,
        'overdue_invoices': overdue_invoices,
        'other_invoices': other_invoices,
        'overdue_amount': overdue_amount,
        'payment_percentage': (total_paid / total_purchases * 100) if total_purchases > 0 else 0,
        'recent_orders': recent_orders,
        'recent_invoices': recent_invoices,
    }
    return render(request, 'purchases/supplier_detail.html', context)

@login_required
def supplier_delete(request, pk):
    """حذف مورد"""
    supplier = get_object_or_404(Supplier, pk=pk)
    supplier_name = supplier.name
    supplier.delete()
    messages.success(request, f'تم حذف المورد "{supplier_name}" بنجاح')
    return redirect('purchases:supplier_list')

# ========== إدارة المنتجات ==========
@login_required
def product_list(request):
    """قائمة المنتجات"""
    search = request.GET.get('search', '')
    low_stock = request.GET.get('low_stock', '')
    products = Product.objects.all()

    if search:
        products = products.filter(
            Q(name__icontains=search) |
            Q(code__icontains=search)
        )

    if low_stock:
        products = products.extra(where=["stock_quantity <= min_stock_level"])

    paginator = Paginator(products, 20)
    page_number = request.GET.get('page')
    products = paginator.get_page(page_number)

    context = {
        'products': products,
        'search': search,
        'low_stock': low_stock,
    }
    return render(request, 'purchases/product_list.html', context)

@login_required
def product_create(request):
    """إضافة منتج جديد"""
    if request.method == 'POST':
        form = ProductForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة المنتج بنجاح')
            return redirect('purchases:product_list')
    else:
        form = ProductForm()

    context = {'form': form, 'title': 'إضافة منتج جديد'}
    return render(request, 'purchases/product_form.html', context)

@login_required
def product_edit(request, pk):
    """تعديل منتج"""
    product = get_object_or_404(Product, pk=pk)
    if request.method == 'POST':
        form = ProductForm(request.POST, instance=product)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث بيانات المنتج بنجاح')
            return redirect('purchases:product_list')
    else:
        form = ProductForm(instance=product)

    context = {'form': form, 'title': 'تعديل المنتج', 'product': product}
    return render(request, 'purchases/product_form.html', context)

@login_required
def product_delete(request, pk):
    """حذف منتج"""
    product = get_object_or_404(Product, pk=pk)
    if request.method == 'POST':
        product.delete()
        messages.success(request, 'تم حذف المنتج بنجاح')
        return redirect('purchases:product_list')

    context = {'product': product}
    return render(request, 'purchases/product_confirm_delete.html', context)

# ========== إدارة أوامر الشراء ==========
@login_required
def order_list(request):
    """قائمة أوامر الشراء"""
    search = request.GET.get('search', '')
    status = request.GET.get('status', '')
    orders = PurchaseOrder.objects.select_related('supplier', 'created_by')

    if search:
        orders = orders.filter(
            Q(order_number__icontains=search) |
            Q(supplier__name__icontains=search)
        )

    if status:
        orders = orders.filter(status=status)

    paginator = Paginator(orders, 20)
    page_number = request.GET.get('page')
    orders = paginator.get_page(page_number)

    # إحصائيات للقائمة
    draft_orders_count = PurchaseOrder.objects.filter(status='draft').count()
    confirmed_orders_count = PurchaseOrder.objects.filter(status='confirmed').count()

    # حساب المبلغ الإجمالي من خلال الأصناف
    total_amount = 0
    for order in PurchaseOrder.objects.all():
        total_amount += order.total_amount

    context = {
        'orders': orders,
        'search': search,
        'status': status,
        'draft_orders_count': draft_orders_count,
        'confirmed_orders_count': confirmed_orders_count,
        'total_amount': total_amount,
    }
    return render(request, 'purchases/purchase_order_list.html', context)


@login_required
def purchase_order_detail(request, pk):
    """تفاصيل أمر الشراء"""
    order = get_object_or_404(PurchaseOrder, pk=pk)

    context = {
        'order': order,
    }
    return render(request, 'purchases/purchase_order_detail.html', context)


@login_required
def purchase_order_create(request):
    """إنشاء أمر شراء جديد"""
    if request.method == 'POST':
        form = PurchaseOrderForm(request.POST)
        if form.is_valid():
            order = form.save(commit=False)
            order.created_by = request.user
            order.save()

            # إضافة الأصناف
            items_data = []
            counter = 1
            while f'product_{counter}' in request.POST:
                product_id = request.POST.get(f'product_{counter}')
                quantity = request.POST.get(f'quantity_{counter}')
                unit_price = request.POST.get(f'unit_price_{counter}')
                discount = request.POST.get(f'discount_{counter}', 0)

                if product_id and quantity and unit_price:
                    try:
                        product = Product.objects.get(id=product_id)
                        PurchaseOrderItem.objects.create(
                            order=order,
                            product=product,
                            quantity=float(quantity),
                            unit_price=float(unit_price),
                            discount_percentage=float(discount)
                        )
                    except (Product.DoesNotExist, ValueError):
                        pass

                counter += 1

            # حساب الإجماليات
            order.calculate_totals()

            messages.success(request, f'تم إنشاء أمر الشراء {order.order_number} بنجاح')
            return redirect('purchases:purchase_order_detail', pk=order.pk)
    else:
        form = PurchaseOrderForm()

    suppliers = Supplier.objects.filter(is_active=True)
    products = Product.objects.filter(is_active=True)
    products_json = json.dumps([{
        'id': p.id,
        'name': p.name,
        'code': p.code,
        'cost_price': float(p.purchase_price or 0)
    } for p in products])

    context = {
        'form': form,
        'suppliers': suppliers,
        'products': products,
        'products_json': products_json,
    }
    return render(request, 'purchases/purchase_order_form.html', context)


@login_required
def purchase_order_edit(request, pk):
    """تعديل أمر الشراء"""
    order = get_object_or_404(PurchaseOrder, pk=pk)

    if order.status != 'draft':
        messages.error(request, 'لا يمكن تعديل أمر الشراء بعد إرساله')
        return redirect('purchases:purchase_order_detail', pk=pk)

    if request.method == 'POST':
        form = PurchaseOrderForm(request.POST, instance=order)
        if form.is_valid():
            order = form.save()

            # حذف الأصناف الموجودة
            order.items.all().delete()

            # إضافة الأصناف الجديدة
            counter = 1
            while f'product_{counter}' in request.POST:
                product_id = request.POST.get(f'product_{counter}')
                quantity = request.POST.get(f'quantity_{counter}')
                unit_price = request.POST.get(f'unit_price_{counter}')
                discount = request.POST.get(f'discount_{counter}', 0)

                if product_id and quantity and unit_price:
                    try:
                        product = Product.objects.get(id=product_id)
                        PurchaseOrderItem.objects.create(
                            order=order,
                            product=product,
                            quantity=float(quantity),
                            unit_price=float(unit_price),
                            discount_percentage=float(discount)
                        )
                    except (Product.DoesNotExist, ValueError):
                        pass

                counter += 1

            # حساب الإجماليات
            order.calculate_totals()

            messages.success(request, 'تم تحديث أمر الشراء بنجاح')
            return redirect('purchases:purchase_order_detail', pk=order.pk)
    else:
        form = PurchaseOrderForm(instance=order)

    suppliers = Supplier.objects.filter(is_active=True)
    products = Product.objects.filter(is_active=True)
    products_json = json.dumps([{
        'id': p.id,
        'name': p.name,
        'code': p.code,
        'cost_price': float(p.purchase_price or 0)
    } for p in products])

    context = {
        'form': form,
        'order': order,
        'suppliers': suppliers,
        'products': products,
        'products_json': products_json,
    }
    return render(request, 'purchases/purchase_order_form.html', context)


@login_required
def purchase_order_delete(request, pk):
    """حذف أمر الشراء"""
    order = get_object_or_404(PurchaseOrder, pk=pk)

    if order.status != 'draft':
        messages.error(request, 'لا يمكن حذف أمر الشراء بعد إرساله')
        return redirect('purchases:purchase_order_detail', pk=pk)

    order_number = order.order_number
    order.delete()

    messages.success(request, f'تم حذف أمر الشراء {order_number} بنجاح')
    return redirect('purchases:purchase_order_list')


@login_required
def purchase_order_print(request, pk):
    """طباعة أمر الشراء"""
    order = get_object_or_404(PurchaseOrder, pk=pk)

    context = {
        'order': order,
    }
    return render(request, 'purchases/purchase_order_print.html', context)


# ========== فواتير الشراء ==========
@login_required
def purchase_invoice_list(request):
    """قائمة فواتير الشراء"""
    search = request.GET.get('search', '')
    status = request.GET.get('status', '')

    invoices = PurchaseInvoice.objects.select_related('supplier')

    if search:
        invoices = invoices.filter(
            Q(invoice_number__icontains=search) |
            Q(supplier__name__icontains=search)
        )

    if status:
        invoices = invoices.filter(status=status)

    paginator = Paginator(invoices, 20)
    page_number = request.GET.get('page')
    invoices = paginator.get_page(page_number)

    context = {
        'invoices': invoices,
        'search': search,
        'status': status,
    }
    return render(request, 'purchases/purchase_invoice_list.html', context)


@login_required
def purchase_invoice_create(request):
    """إنشاء فاتورة شراء جديدة"""
    order_id = request.GET.get('order')
    order = None

    if order_id:
        order = get_object_or_404(PurchaseOrder, pk=order_id)

    if request.method == 'POST':
        form = PurchaseInvoiceForm(request.POST)
        if form.is_valid():
            invoice = form.save(commit=False)
            invoice.created_by = request.user

            # إنشاء رقم فاتورة تلقائي إذا لم يتم تحديده
            if not invoice.invoice_number:
                invoice.invoice_number = f"INV-{timezone.now().strftime('%Y%m%d')}-{PurchaseInvoice.objects.count() + 1:04d}"

            invoice.save()

            # إذا كانت الفاتورة من أمر شراء، نسخ الأصناف
            if order:
                for order_item in order.items.all():
                    PurchaseInvoiceItem.objects.create(
                        invoice=invoice,
                        product=order_item.product,
                        quantity=order_item.quantity,
                        unit_price=order_item.unit_price,
                        discount_percentage=order_item.discount_percentage
                    )

            # معالجة أصناف الفاتورة من النموذج
            item_counter = 1
            while f'product_{item_counter}' in request.POST:
                product_id = request.POST.get(f'product_{item_counter}')
                warehouse_id = request.POST.get(f'warehouse_{item_counter}')
                quantity = request.POST.get(f'quantity_{item_counter}')
                unit_price = request.POST.get(f'unit_price_{item_counter}')
                discount = request.POST.get(f'discount_{item_counter}', 0)

                if product_id and warehouse_id and quantity and unit_price:
                    try:
                        product = ProductDefinition.objects.get(id=product_id)
                        warehouse = WarehouseDefinition.objects.get(id=warehouse_id)

                        # إنشاء عنصر الفاتورة
                        invoice_item = PurchaseInvoiceItem.objects.create(
                            invoice=invoice,
                            product=product,
                            warehouse=warehouse,
                            quantity=float(quantity),
                            unit_price=float(unit_price),
                            discount_percentage=float(discount)
                        )

                        # إنشاء أو تحديث عنصر المخزون
                        inventory_item, created = InventoryItem.objects.get_or_create(
                            warehouse=warehouse,
                            product=product,
                            defaults={
                                'quantity_on_hand': 0,
                                'minimum_stock': product.minimum_stock or 0,
                                'maximum_stock': product.maximum_stock or 1000,
                                'average_cost': float(unit_price),
                                'last_cost': float(unit_price),
                                'total_value': 0,
                                'is_active': True
                            }
                        )

                        # تحديث كمية المخزون
                        inventory_item.quantity_on_hand += float(quantity)
                        inventory_item.last_cost = float(unit_price)
                        inventory_item.total_value = inventory_item.quantity_on_hand * inventory_item.average_cost
                        inventory_item.save()

                        # إنشاء حركة مخزون
                        InventoryTransaction.objects.create(
                            transaction_number=f"REC-{invoice.invoice_number}-{item_counter}",
                            warehouse=warehouse,
                            product=product,
                            transaction_type='receipt',
                            transaction_reason='purchase_invoice',
                            quantity=float(quantity),
                            unit_cost=float(unit_price),
                            total_cost=float(quantity) * float(unit_price),
                            reference_number=invoice.invoice_number,
                            notes=f"استلام من فاتورة شراء {invoice.invoice_number}",
                            transaction_date=timezone.now(),
                            created_by=request.user
                        )

                    except (ProductDefinition.DoesNotExist, WarehouseDefinition.DoesNotExist, ValueError) as e:
                        messages.warning(request, f'خطأ في معالجة الصنف {item_counter}: {str(e)}')
                        pass

                item_counter += 1

            messages.success(request, f'تم إنشاء فاتورة الشراء {invoice.invoice_number} بنجاح')
            return redirect('purchases:purchase_invoice_detail', pk=invoice.pk)
        else:
            messages.error(request, 'يرجى تصحيح الأخطاء في النموذج')
    else:
        initial_data = {}
        if order:
            initial_data = {
                'supplier': order.supplier,
                'reference_number': order.order_number,
            }
        form = PurchaseInvoiceForm(initial=initial_data)

    suppliers = Supplier.objects.filter(is_active=True)
    products = ProductDefinition.objects.filter(is_active=True)
    # عرض جميع المخازن النشطة مع إعطاء أولوية لمخازن المواد الخام
    warehouses = WarehouseDefinition.objects.filter(is_active=True).order_by(
        Case(
            When(warehouse_type='raw_materials', then=Value(1)),
            When(warehouse_type='main', then=Value(2)),
            default=Value(3),
            output_field=IntegerField()
        ),
        'name'
    )

    context = {
        'form': form,
        'order': order,
        'suppliers': suppliers,
        'products': products,
        'warehouses': warehouses,
        'today': timezone.now().date(),
    }
    return render(request, 'purchases/purchase_invoice_form.html', context)


@login_required
def purchase_invoice_detail(request, pk):
    """تفاصيل فاتورة الشراء"""
    invoice = get_object_or_404(PurchaseInvoice, pk=pk)

    context = {
        'invoice': invoice,
    }
    return render(request, 'purchases/purchase_invoice_detail.html', context)


@login_required
def purchase_invoice_edit(request, pk):
    """تعديل فاتورة الشراء"""
    invoice = get_object_or_404(PurchaseInvoice, pk=pk)

    if request.method == 'POST':
        form = PurchaseInvoiceForm(request.POST, instance=invoice)
        if form.is_valid():
            invoice = form.save()
            messages.success(request, 'تم تحديث فاتورة الشراء بنجاح')
            return redirect('purchases:purchase_invoice_detail', pk=invoice.pk)
    else:
        form = PurchaseInvoiceForm(instance=invoice)

    suppliers = Supplier.objects.filter(is_active=True)
    products = Product.objects.filter(is_active=True)

    context = {
        'form': form,
        'invoice': invoice,
        'suppliers': suppliers,
        'products': products,
    }
    return render(request, 'purchases/purchase_invoice_form.html', context)


@login_required
def purchase_invoice_delete(request, pk):
    """حذف فاتورة الشراء"""
    invoice = get_object_or_404(PurchaseInvoice, pk=pk)

    invoice_number = invoice.invoice_number
    invoice.delete()

    messages.success(request, f'تم حذف فاتورة الشراء {invoice_number} بنجاح')
    return redirect('purchases:purchase_invoice_list')


@login_required
def purchase_invoice_print(request, pk):
    """طباعة فاتورة الشراء"""
    invoice = get_object_or_404(PurchaseInvoice, pk=pk)

    context = {
        'invoice': invoice,
    }
    return render(request, 'purchases/purchase_invoice_print.html', context)


# ========== التقارير ==========
@login_required
def purchase_reports(request):
    """صفحة التقارير الرئيسية"""
    context = {}
    return render(request, 'purchases/reports_dashboard.html', context)


@login_required
def supplier_report(request):
    """تقرير الموردين"""
    search = request.GET.get('search', '')
    supplier_type = request.GET.get('supplier_type', '')

    suppliers = Supplier.objects.all()

    if search:
        suppliers = suppliers.filter(name__icontains=search)

    if supplier_type:
        suppliers = suppliers.filter(supplier_type=supplier_type)

    # إحصائيات
    total_suppliers = suppliers.count()
    active_suppliers = suppliers.filter(is_active=True).count()
    inactive_suppliers = suppliers.filter(is_active=False).count()

    context = {
        'suppliers': suppliers,
        'search': search,
        'supplier_type': supplier_type,
        'total_suppliers': total_suppliers,
        'active_suppliers': active_suppliers,
        'inactive_suppliers': inactive_suppliers,
    }
    return render(request, 'purchases/supplier_report.html', context)


@login_required
def purchase_report(request):
    """تقرير المشتريات"""
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    supplier_id = request.GET.get('supplier', '')

    invoices = PurchaseInvoice.objects.select_related('supplier')

    if date_from:
        invoices = invoices.filter(invoice_date__gte=date_from)

    if date_to:
        invoices = invoices.filter(invoice_date__lte=date_to)

    if supplier_id:
        invoices = invoices.filter(supplier_id=supplier_id)

    # إحصائيات
    total_invoices = invoices.count()
    total_amount = sum(invoice.total_amount for invoice in invoices)
    paid_invoices = invoices.filter(status='paid').count()
    pending_invoices = invoices.filter(status='received').count()

    # حساب متوسط الفاتورة
    average_invoice = total_amount / total_invoices if total_invoices > 0 else 0

    suppliers = Supplier.objects.filter(is_active=True)

    context = {
        'invoices': invoices,
        'date_from': date_from,
        'date_to': date_to,
        'selected_supplier': supplier_id,
        'suppliers': suppliers,
        'total_invoices': total_invoices,
        'total_amount': total_amount,
        'paid_invoices': paid_invoices,
        'pending_invoices': pending_invoices,
        'average_invoice': average_invoice,
    }
    return render(request, 'purchases/purchase_report.html', context)


@login_required
def purchase_inventory_report(request):
    """تقرير مخزون المشتريات"""
    search = request.GET.get('search', '')
    category_id = request.GET.get('category', '')

    # استخدام ProductDefinition من النظام الموحد
    products = ProductDefinition.objects.filter(is_active=True)

    if search:
        products = products.filter(
            Q(name__icontains=search) |
            Q(code__icontains=search)
        )

    if category_id:
        products = products.filter(category_id=category_id)

    # إحصائيات من InventoryItem
    total_products = products.count()

    # تحضير بيانات المنتجات مع معلومات المخزون
    products_with_inventory = []
    low_stock_count = 0
    out_of_stock_count = 0
    total_inventory_value = 0

    for product in products:
        # الحصول على معلومات المخزون من InventoryItem
        inventory_items = InventoryItem.objects.filter(product=product)
        total_quantity = sum(item.quantity_on_hand for item in inventory_items)

        # تحديد حالة المخزون
        stock_status = 'normal'
        if total_quantity == 0:
            stock_status = 'out'
            out_of_stock_count += 1
        elif hasattr(product, 'min_stock_level') and product.min_stock_level and total_quantity <= product.min_stock_level:
            stock_status = 'low'
            low_stock_count += 1

        # حساب قيمة المخزون
        inventory_value = 0
        if product.cost_price and total_quantity:
            inventory_value = total_quantity * product.cost_price
            total_inventory_value += inventory_value

        # إضافة البيانات المحضرة
        products_with_inventory.append({
            'product': product,
            'total_quantity': total_quantity,
            'inventory_value': inventory_value,
            'stock_status': stock_status,
        })

    # الحصول على الفئات من ProductCategory
    from definitions.models import ProductCategory
    categories = ProductCategory.objects.filter(is_active=True)

    context = {
        'products_with_inventory': products_with_inventory,
        'search': search,
        'selected_category': category_id,
        'categories': categories,
        'total_products': total_products,
        'low_stock_products': low_stock_count,
        'out_of_stock_products': out_of_stock_count,
        'total_inventory_value': total_inventory_value,
    }
    return render(request, 'purchases/purchase_inventory_report.html', context)


@login_required
def financial_report(request):
    """التقرير المالي للمشتريات"""
    from django.db.models import Sum, Count
    from datetime import datetime, timedelta

    # الفلاتر
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    supplier_id = request.GET.get('supplier', '')

    # تحديد الفترة الافتراضية (آخر 12 شهر)
    if not date_from:
        date_from = (timezone.now() - timedelta(days=365)).date()
    else:
        date_from = datetime.strptime(date_from, '%Y-%m-%d').date()

    if not date_to:
        date_to = timezone.now().date()
    else:
        date_to = datetime.strptime(date_to, '%Y-%m-%d').date()

    # البحث في الفواتير
    invoices = PurchaseInvoice.objects.filter(
        invoice_date__gte=date_from,
        invoice_date__lte=date_to
    ).select_related('supplier')

    if supplier_id:
        invoices = invoices.filter(supplier_id=supplier_id)

    # الإحصائيات المالية
    total_invoices = invoices.count()

    # حساب الإجماليات باستخدام property
    total_amount = sum(invoice.total_amount for invoice in invoices)
    paid_amount = sum(invoice.total_amount for invoice in invoices.filter(status='paid'))
    pending_amount = sum(invoice.total_amount for invoice in invoices.filter(status='received'))
    overdue_invoices = invoices.filter(
        status='received',
        due_date__lt=timezone.now().date()
    )
    overdue_amount = sum(invoice.total_amount for invoice in overdue_invoices)

    # تحليل شهري
    monthly_data = []
    current_date = date_from
    while current_date <= date_to:
        month_start = current_date.replace(day=1)
        if current_date.month == 12:
            month_end = current_date.replace(year=current_date.year + 1, month=1, day=1) - timedelta(days=1)
        else:
            month_end = current_date.replace(month=current_date.month + 1, day=1) - timedelta(days=1)

        month_invoices = invoices.filter(
            invoice_date__gte=month_start,
            invoice_date__lte=month_end
        )

        month_total = sum(invoice.total_amount for invoice in month_invoices)
        month_count = month_invoices.count()

        monthly_data.append({
            'month': month_start.strftime('%Y-%m'),
            'month_name': month_start.strftime('%B %Y'),
            'total_amount': month_total,
            'invoice_count': month_count,
            'average': month_total / month_count if month_count > 0 else 0
        })

        if current_date.month == 12:
            current_date = current_date.replace(year=current_date.year + 1, month=1)
        else:
            current_date = current_date.replace(month=current_date.month + 1)

    # تحليل الموردين
    supplier_data = {}
    for invoice in invoices:
        supplier_id = invoice.supplier.id
        supplier_name = invoice.supplier.name

        if supplier_id not in supplier_data:
            supplier_data[supplier_id] = {
                'supplier__name': supplier_name,
                'supplier__id': supplier_id,
                'total_amount': 0,
                'invoice_count': 0
            }

        supplier_data[supplier_id]['total_amount'] += invoice.total_amount
        supplier_data[supplier_id]['invoice_count'] += 1

    # حساب المتوسط وترتيب النتائج
    supplier_analysis = []
    for supplier_id, data in supplier_data.items():
        data['avg_amount'] = data['total_amount'] / data['invoice_count'] if data['invoice_count'] > 0 else 0
        supplier_analysis.append(data)

    # ترتيب حسب إجمالي المبلغ
    supplier_analysis = sorted(supplier_analysis, key=lambda x: x['total_amount'], reverse=True)[:10]

    # الموردين للفلترة
    suppliers = Supplier.objects.filter(is_active=True)

    context = {
        'date_from': date_from,
        'date_to': date_to,
        'selected_supplier': supplier_id,
        'suppliers': suppliers,
        'total_invoices': total_invoices,
        'total_amount': total_amount,
        'paid_amount': paid_amount,
        'pending_amount': pending_amount,
        'overdue_amount': overdue_amount,
        'monthly_data': monthly_data,
        'supplier_analysis': supplier_analysis,
        'payment_percentage': (paid_amount / total_amount * 100) if total_amount > 0 else 0,
    }

    return render(request, 'purchases/financial_report.html', context)


@login_required
def suppliers_financial_summary(request):
    """ملخص مالي شامل لجميع الموردين"""
    suppliers = Supplier.objects.filter(is_active=True)

    suppliers_data = []
    total_debt = 0
    total_paid = 0
    total_purchases = 0

    for supplier in suppliers:
        invoices = PurchaseInvoice.objects.filter(supplier=supplier)

        supplier_total = sum(invoice.total_amount for invoice in invoices)
        supplier_paid = sum(invoice.paid_amount for invoice in invoices)
        supplier_remaining = sum(invoice.remaining_amount for invoice in invoices)

        if supplier_total > 0:  # فقط الموردين الذين لديهم معاملات
            suppliers_data.append({
                'supplier': supplier,
                'total_purchases': supplier_total,
                'total_paid': supplier_paid,
                'total_remaining': supplier_remaining,
                'payment_percentage': (supplier_paid / supplier_total * 100) if supplier_total > 0 else 0,
                'invoices_count': invoices.count(),
                'paid_invoices': invoices.filter(status='paid').count(),
                'partially_paid_invoices': invoices.filter(status='partially_paid').count(),
                'overdue_invoices': invoices.filter(status='overdue').count(),
            })

            total_debt += supplier_remaining
            total_paid += supplier_paid
            total_purchases += supplier_total

    # ترتيب حسب المبلغ المتبقي (الأعلى أولاً)
    suppliers_data.sort(key=lambda x: x['total_remaining'], reverse=True)

    # حساب متوسط الدين
    average_debt = total_debt / len(suppliers_data) if len(suppliers_data) > 0 else 0

    context = {
        'suppliers_data': suppliers_data,
        'total_debt': total_debt,
        'total_paid': total_paid,
        'total_purchases': total_purchases,
        'overall_payment_percentage': (total_paid / total_purchases * 100) if total_purchases > 0 else 0,
        'suppliers_count': len(suppliers_data),
        'average_debt': average_debt,
    }

    return render(request, 'purchases/suppliers_financial_summary.html', context)


# إضافة aliases للوظائف المفقودة
def order_create(request):
    """إنشاء أمر شراء جديد"""
    if request.method == 'POST':
        # معالجة البيانات المرسلة
        supplier_id = request.POST.get('supplier')
        order_date = request.POST.get('order_date')

        if supplier_id and order_date:
            try:
                supplier = Supplier.objects.get(id=supplier_id)
                order = PurchaseOrder.objects.create(
                    supplier=supplier,
                    order_date=order_date,
                    created_by=request.user,
                    status='draft'
                )

                # إضافة الأصناف
                counter = 1
                total_amount = 0
                while f'product_{counter}' in request.POST:
                    product_id = request.POST.get(f'product_{counter}')
                    quantity = request.POST.get(f'quantity_{counter}')
                    unit_price = request.POST.get(f'unit_price_{counter}')
                    discount = request.POST.get(f'discount_{counter}', 0)

                    if product_id and quantity and unit_price:
                        try:
                            product = Product.objects.get(id=product_id)
                            item = PurchaseOrderItem.objects.create(
                                order=order,
                                product=product,
                                quantity=float(quantity),
                                unit_price=float(unit_price),
                                discount_percentage=float(discount) if discount else 0
                            )
                            total_amount += item.total_price
                        except (Product.DoesNotExist, ValueError):
                            pass

                    counter += 1

                # المبلغ الإجمالي يُحسب تلقائياً من خلال property
                order.save()

                messages.success(request, f'تم إنشاء أمر الشراء {order.order_number} بنجاح')
                return redirect('purchases:purchase_order_detail', pk=order.pk)

            except Supplier.DoesNotExist:
                messages.error(request, 'المورد المحدد غير موجود')
        else:
            messages.error(request, 'يرجى ملء جميع الحقول المطلوبة')

    # عرض النموذج
    suppliers = Supplier.objects.filter(is_active=True)
    products = Product.objects.filter(is_active=True)
    products_json = json.dumps([{
        'id': p.id,
        'name': p.name,
        'code': p.code,
        'cost_price': float(p.purchase_price or 0)
    } for p in products])

    context = {
        'suppliers': suppliers,
        'products': products,
        'products_json': products_json,
    }
    return render(request, 'purchases/purchase_order_form.html', context)


def order_detail(request, pk):
    """تفاصيل أمر الشراء"""
    order = get_object_or_404(PurchaseOrder, pk=pk)
    context = {'order': order}
    return render(request, 'purchases/purchase_order_detail.html', context)


def order_edit(request, pk):
    """تعديل أمر الشراء"""
    order = get_object_or_404(PurchaseOrder, pk=pk)

    if order.status != 'draft':
        messages.error(request, 'لا يمكن تعديل أمر الشراء بعد إرساله')
        return redirect('purchases:purchase_order_detail', pk=pk)

    if request.method == 'POST':
        # معالجة التعديل
        supplier_id = request.POST.get('supplier')
        if supplier_id:
            try:
                supplier = Supplier.objects.get(id=supplier_id)
                order.supplier = supplier
                order.order_date = request.POST.get('order_date', order.order_date)
                order.save()

                messages.success(request, 'تم تحديث أمر الشراء بنجاح')
                return redirect('purchases:purchase_order_detail', pk=order.pk)
            except Supplier.DoesNotExist:
                messages.error(request, 'المورد المحدد غير موجود')

    suppliers = Supplier.objects.filter(is_active=True)
    products = Product.objects.filter(is_active=True)
    products_json = json.dumps([{
        'id': p.id,
        'name': p.name,
        'code': p.code,
        'cost_price': float(p.purchase_price or 0)
    } for p in products])

    context = {
        'order': order,
        'suppliers': suppliers,
        'products': products,
        'products_json': products_json,
    }
    return render(request, 'purchases/purchase_order_form.html', context)


def order_delete(request, pk):
    """حذف أمر الشراء"""
    order = get_object_or_404(PurchaseOrder, pk=pk)

    if order.status != 'draft':
        messages.error(request, 'لا يمكن حذف أمر الشراء بعد إرساله')
        return redirect('purchases:purchase_order_detail', pk=pk)

    order_number = order.order_number
    order.delete()

    messages.success(request, f'تم حذف أمر الشراء {order_number} بنجاح')
    return redirect('purchases:purchase_order_list')


def order_print(request, pk):
    """طباعة أمر الشراء"""
    order = get_object_or_404(PurchaseOrder, pk=pk)
    context = {'order': order}
    return render(request, 'purchases/purchase_order_print.html', context)


# وظائف الفواتير
def invoice_list(request):
    """قائمة فواتير الشراء"""
    invoices = PurchaseInvoice.objects.select_related('supplier').order_by('-created_at')

    # البحث والفلترة
    search = request.GET.get('search', '')
    status_filter = request.GET.get('status', '')
    supplier_filter = request.GET.get('supplier', '')

    if search:
        invoices = invoices.filter(
            Q(invoice_number__icontains=search) |
            Q(supplier__name__icontains=search)
        )

    if status_filter:
        invoices = invoices.filter(status=status_filter)

    if supplier_filter:
        invoices = invoices.filter(supplier_id=supplier_filter)

    # إحصائيات
    total_invoices = invoices.count()
    paid_invoices = invoices.filter(status='paid').count()
    pending_invoices = invoices.filter(status='received').count()
    total_amount = sum(invoice.total_amount for invoice in invoices)

    # الترقيم
    paginator = Paginator(invoices, 20)
    page_number = request.GET.get('page')
    invoices = paginator.get_page(page_number)

    # قائمة الموردين للفلترة
    suppliers = Supplier.objects.filter(is_active=True)

    context = {
        'invoices': invoices,
        'search': search,
        'suppliers': suppliers,
        'total_invoices': total_invoices,
        'paid_invoices': paid_invoices,
        'pending_invoices': pending_invoices,
        'total_amount': total_amount,
    }
    return render(request, 'purchases/purchase_invoice_list.html', context)


def invoice_create(request):
    """إنشاء فاتورة شراء جديدة"""
    if request.method == 'POST':
        try:
            # بيانات الفاتورة الأساسية
            supplier_id = request.POST.get('supplier')
            total_amount = request.POST.get('total_amount', 0)
            invoice_date_str = request.POST.get('invoice_date')
            due_date_str = request.POST.get('due_date')

            # التحقق من البيانات الأساسية
            if not supplier_id:
                messages.error(request, '❌ يرجى اختيار المورد')
                return redirect('purchases:purchase_invoice_create')

            try:
                total_amount = float(total_amount) if total_amount else 0
            except ValueError:
                total_amount = 0

            # تحويل التواريخ بشكل آمن
            from datetime import datetime

            # تاريخ الفاتورة
            if invoice_date_str:
                try:
                    invoice_date = datetime.strptime(invoice_date_str, '%Y-%m-%d').date()
                except ValueError:
                    invoice_date = timezone.now().date()
            else:
                invoice_date = timezone.now().date()

            # تاريخ الاستحقاق
            due_date = None
            if due_date_str:
                try:
                    due_date = datetime.strptime(due_date_str, '%Y-%m-%d').date()
                except ValueError:
                    due_date = None

            # إنشاء رقم فاتورة تلقائي
            invoice_number = f"INV-{timezone.now().strftime('%Y%m%d')}-{PurchaseInvoice.objects.count() + 1:04d}"

            # إنشاء الفاتورة بأبسط طريقة
            invoice = PurchaseInvoice.objects.create(
                invoice_number=invoice_number,
                supplier_id=supplier_id,
                invoice_date=invoice_date,
                due_date=due_date,
                status='received',
                notes=request.POST.get('notes', ''),
                total_amount=total_amount,
                paid_amount_manual=float(request.POST.get('paid_amount_manual', 0)),
                created_by=request.user
            )

            # معالجة أصناف الفاتورة من النموذج
            item_counter = 1
            while f'product_{item_counter}' in request.POST:
                product_id = request.POST.get(f'product_{item_counter}')
                warehouse_id = request.POST.get(f'warehouse_{item_counter}')
                quantity = request.POST.get(f'quantity_{item_counter}')
                unit_price = request.POST.get(f'unit_price_{item_counter}')
                discount = request.POST.get(f'discount_{item_counter}', 0)

                if product_id and warehouse_id and quantity and unit_price:
                    try:
                        product = ProductDefinition.objects.get(id=product_id)
                        warehouse = WarehouseDefinition.objects.get(id=warehouse_id)

                        # إنشاء عنصر الفاتورة
                        PurchaseInvoiceItem.objects.create(
                            invoice=invoice,
                            product=product,
                            warehouse=warehouse,
                            quantity=float(quantity) if quantity else 0,
                            unit_price=float(unit_price) if unit_price else 0,
                            discount_percentage=float(discount) if discount else 0
                        )

                    except (ProductDefinition.DoesNotExist, WarehouseDefinition.DoesNotExist, ValueError) as e:
                        messages.warning(request, f'خطأ في معالجة الصنف {item_counter}: {str(e)}')
                        pass

                item_counter += 1

            # رسالة نجاح
            messages.success(request, f'✅ تم حفظ الفاتورة {invoice.invoice_number} بنجاح!')
            return redirect('purchases:purchase_invoice_list')

        except Exception as e:
            messages.error(request, f'❌ خطأ: {str(e)}')

    # عرض النموذج
    suppliers = Supplier.objects.filter(is_active=True)
    products = ProductDefinition.objects.filter(is_active=True)
    warehouses = WarehouseDefinition.objects.filter(is_active=True).order_by(
        Case(
            When(warehouse_type='raw_materials', then=Value(1)),
            When(warehouse_type='main', then=Value(2)),
            default=Value(3),
            output_field=IntegerField()
        ),
        'name'
    )

    context = {
        'suppliers': suppliers,
        'products': products,
        'warehouses': warehouses,
        'today': timezone.now().date(),
    }
    return render(request, 'purchases/purchase_invoice_form.html', context)


def invoice_detail(request, pk):
    """تفاصيل فاتورة الشراء مع بيانات المدفوعات"""
    invoice = get_object_or_404(PurchaseInvoice, pk=pk)

    # التحقق من حالة التطبيق على المخزون
    all_items_applied = all(item.applied_to_stock for item in invoice.items.all())

    # جلب المدفوعات المرتبطة بالفاتورة
    payments = []
    for allocation in invoice.payment_allocations.all():
        payments.append(allocation.payment)

    # ترتيب المدفوعات حسب التاريخ
    payments.sort(key=lambda x: x.payment_date, reverse=True)

    context = {
        'invoice': invoice,
        'all_items_applied': all_items_applied,
        'payments': payments,
        'today': timezone.now().date(),
    }
    return render(request, 'purchases/purchase_invoice_detail.html', context)


def invoice_edit(request, pk):
    """تعديل فاتورة الشراء"""
    invoice = get_object_or_404(PurchaseInvoice, pk=pk)

    if request.method == 'POST':
        messages.success(request, 'تم تحديث فاتورة الشراء بنجاح')
        return redirect('purchases:purchase_invoice_detail', pk=invoice.pk)

    suppliers = Supplier.objects.filter(is_active=True)
    products = ProductDefinition.objects.filter(is_active=True)
    warehouses = WarehouseDefinition.objects.filter(is_active=True)

    context = {
        'invoice': invoice,
        'suppliers': suppliers,
        'products': products,
        'warehouses': warehouses,
        'today': timezone.now().date(),
    }
    return render(request, 'purchases/purchase_invoice_form.html', context)


def invoice_delete(request, pk):
    """حذف فاتورة الشراء"""
    invoice = get_object_or_404(PurchaseInvoice, pk=pk)
    invoice_number = invoice.invoice_number
    invoice.delete()

    messages.success(request, f'تم حذف فاتورة الشراء {invoice_number} بنجاح')
    return redirect('purchases:purchase_invoice_list')


def invoice_print(request, pk):
    """طباعة فاتورة الشراء"""
    invoice = get_object_or_404(PurchaseInvoice, pk=pk)
    context = {
        'invoice': invoice,
        'today': timezone.now(),
    }
    return render(request, 'purchases/purchase_invoice_print.html', context)


def supplier_payment_create(request, supplier_id):
    """إنشاء دفعة جديدة للمورد مع توزيع على الفواتير"""
    supplier = get_object_or_404(Supplier, pk=supplier_id)

    # الحصول على الفواتير غير المدفوعة بالكامل
    unpaid_invoices = PurchaseInvoice.objects.filter(
        supplier=supplier,
        status__in=['received', 'partially_paid', 'overdue']
    ).exclude(status='paid').order_by('due_date')

    if request.method == 'POST':
        try:
            # إنشاء الدفعة الرئيسية
            payment = SupplierPayment.objects.create(
                supplier=supplier,
                payment_date=request.POST.get('payment_date'),
                total_amount=request.POST.get('total_amount'),
                payment_method=request.POST.get('payment_method'),
                reference_number=request.POST.get('reference_number', ''),
                notes=request.POST.get('notes', ''),
                created_by=request.user
            )

            # توزيع الدفعة على الفواتير
            total_allocated = 0
            for invoice in unpaid_invoices:
                allocated_amount = request.POST.get(f'allocation_{invoice.id}')
                if allocated_amount and float(allocated_amount) > 0:
                    PaymentAllocation.objects.create(
                        payment=payment,
                        invoice=invoice,
                        allocated_amount=allocated_amount
                    )
                    total_allocated += float(allocated_amount)

            # التحقق من صحة التوزيع
            if total_allocated > float(payment.total_amount):
                payment.delete()
                messages.error(request, 'إجمالي المبالغ المخصصة يتجاوز إجمالي الدفعة')
                return redirect('purchases:supplier_payment_create', supplier_id=supplier_id)

            messages.success(request, f'تم إنشاء دفعة بقيمة {payment.total_amount} ج.م بنجاح')
            return redirect('purchases:supplier_detail', pk=supplier_id)

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إنشاء الدفعة: {str(e)}')

    context = {
        'supplier': supplier,
        'unpaid_invoices': unpaid_invoices,
        'total_debt': sum(invoice.remaining_amount for invoice in unpaid_invoices),
        'today': timezone.now().date(),
    }
    return render(request, 'purchases/supplier_payment_form.html', context)


def alerts_dashboard(request):
    """لوحة التنبيهات والإشعارات"""
    try:
        # تحديث التنبيهات
        AlertManager.create_due_soon_alerts()
        AlertManager.create_overdue_alerts()
        AlertManager.check_credit_limits()
    except Exception as e:
        messages.warning(request, f'تحذير: خطأ في تحديث التنبيهات: {str(e)}')

    # الحصول على التنبيهات
    unread_alerts = PaymentAlert.objects.filter(is_read=False, is_resolved=False).order_by('-created_at')
    critical_alerts = unread_alerts.filter(priority='critical')
    high_alerts = unread_alerts.filter(priority='high')

    # الموافقات المعلقة
    pending_approvals = PaymentApproval.objects.filter(status='pending').order_by('-requested_at')

    # إحصائيات
    total_alerts = unread_alerts.count()
    overdue_count = unread_alerts.filter(alert_type='overdue').count()
    due_soon_count = unread_alerts.filter(alert_type='due_soon').count()

    context = {
        'unread_alerts': unread_alerts[:10],  # أحدث 10 تنبيهات
        'critical_alerts': critical_alerts,
        'high_alerts': high_alerts,
        'pending_approvals': pending_approvals,
        'total_alerts': total_alerts,
        'overdue_count': overdue_count,
        'due_soon_count': due_soon_count,
    }
    return render(request, 'purchases/alerts_dashboard.html', context)


def alert_detail(request, alert_id):
    """تفاصيل التنبيه"""
    alert = get_object_or_404(PaymentAlert, pk=alert_id)

    if request.method == 'POST':
        action = request.POST.get('action')
        if action == 'mark_read':
            alert.mark_as_read(request.user)
        elif action == 'resolve':
            alert.resolve(request.user)

        messages.success(request, 'تم تحديث التنبيه بنجاح')
        return redirect('purchases:alerts_dashboard')

    context = {'alert': alert}
    return render(request, 'purchases/alert_detail.html', context)


def payment_approvals(request):
    """صفحة الموافقات على المدفوعات"""
    pending_approvals = PaymentApproval.objects.filter(status='pending').order_by('-requested_at')
    approved_approvals = PaymentApproval.objects.filter(status='approved').order_by('-approved_at')[:10]

    context = {
        'pending_approvals': pending_approvals,
        'approved_approvals': approved_approvals,
    }
    return render(request, 'purchases/payment_approvals.html', context)


def approve_payment(request, approval_id):
    """الموافقة على دفعة"""
    approval = get_object_or_404(PaymentApproval, pk=approval_id)

    if request.method == 'POST':
        action = request.POST.get('action')
        notes = request.POST.get('notes', '')

        if action == 'approve':
            approval.approve(request.user, notes)
            messages.success(request, f'تم الموافقة على دفعة {approval.payment.total_amount} ج.م')
        elif action == 'reject':
            reason = request.POST.get('rejection_reason', '')
            approval.reject(request.user, reason)
            messages.warning(request, f'تم رفض دفعة {approval.payment.total_amount} ج.م')

        return redirect('purchases:payment_approvals')

    context = {'approval': approval}
    return render(request, 'purchases/approve_payment.html', context)


def supplier_risk_assessment(request, supplier_id):
    """تقييم مخاطر المورد"""
    supplier = get_object_or_404(Supplier, pk=supplier_id)

    # الحصول على أو إنشاء تقييم المخاطر
    risk_assessment, created = SupplierRiskAssessment.objects.get_or_create(
        supplier=supplier,
        defaults={'assessed_by': request.user}
    )

    if request.method == 'POST':
        # تحديث التقييم
        risk_assessment.payment_history_score = int(request.POST.get('payment_history_score', 0))
        risk_assessment.credit_utilization = float(request.POST.get('credit_utilization', 0))
        risk_assessment.average_payment_delay = int(request.POST.get('average_payment_delay', 0))
        risk_assessment.order_fulfillment_rate = float(request.POST.get('order_fulfillment_rate', 100))
        risk_assessment.quality_score = int(request.POST.get('quality_score', 100))
        risk_assessment.delivery_performance = int(request.POST.get('delivery_performance', 100))
        risk_assessment.notes = request.POST.get('notes', '')
        risk_assessment.assessed_by = request.user

        # حساب درجة المخاطر
        risk_assessment.calculate_risk_score()

        messages.success(request, 'تم تحديث تقييم المخاطر بنجاح')
        return redirect('purchases:supplier_detail', pk=supplier_id)

    # تحديث تاريخ الدفع من البيانات الفعلية
    risk_assessment.update_payment_history()

    context = {
        'supplier': supplier,
        'risk_assessment': risk_assessment,
    }
    return render(request, 'purchases/supplier_risk_assessment.html', context)


def cash_flow_forecast(request):
    """توقعات التدفق النقدي"""
    if request.method == 'POST':
        # إنشاء توقع جديد
        forecast = CashFlowForecast.objects.create(
            period_type=request.POST.get('period_type'),
            start_date=request.POST.get('start_date'),
            end_date=request.POST.get('end_date'),
            created_by=request.user
        )

        # حساب التوقعات
        forecast.calculate_forecast()

        messages.success(request, 'تم إنشاء توقع التدفق النقدي بنجاح')
        return redirect('purchases:cash_flow_forecast')

    # الحصول على التوقعات الحالية
    forecasts = CashFlowForecast.objects.all()[:10]

    context = {
        'forecasts': forecasts,
        'today': timezone.now().date(),
    }
    return render(request, 'purchases/cash_flow_forecast.html', context)


def reports_dashboard(request):
    """لوحة تقارير المشتريات"""
    context = {}
    return render(request, 'purchases/reports_dashboard.html', context)


def apply_invoice_to_stock(request, pk):
    """تطبيق فاتورة الشراء على المخزون"""
    if request.method == 'POST':
        invoice = get_object_or_404(PurchaseInvoice, pk=pk)

        try:
            applied_items = 0
            errors = []

            for item in invoice.items.all():
                if not item.applied_to_stock:
                    if item.warehouse:
                        success = item.apply_to_stock(request.user)
                        if success:
                            applied_items += 1
                        else:
                            errors.append(f"فشل في تطبيق الصنف {item.product.name}")
                    else:
                        errors.append(f"لم يتم تحديد مخزن للصنف {item.product.name}")

            if applied_items > 0:
                # تحديث حالة الفاتورة إلى مستلمة إذا لم تكن كذلك
                if invoice.status == 'draft':
                    invoice.status = 'received'
                    invoice.save()

                return JsonResponse({
                    'success': True,
                    'message': f'تم تطبيق {applied_items} صنف على المخزون بنجاح',
                    'applied_items': applied_items,
                    'errors': errors
                })
            else:
                return JsonResponse({
                    'success': False,
                    'message': 'لم يتم تطبيق أي صنف. ' + '; '.join(errors)
                })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'حدث خطأ: {str(e)}'
            })

    return JsonResponse({'success': False, 'message': 'طريقة غير مسموحة'})


@login_required
def add_payment(request, pk):
    """إضافة دفعة جديدة لفاتورة الشراء"""
    invoice = get_object_or_404(PurchaseInvoice, pk=pk)

    if request.method == 'POST':
        try:
            amount = float(request.POST.get('amount', 0))
            payment_date = request.POST.get('payment_date')
            payment_method = request.POST.get('payment_method')
            reference_number = request.POST.get('reference_number', '')
            notes = request.POST.get('notes', '')

            # التحقق من صحة المبلغ
            if amount <= 0:
                messages.error(request, '❌ يرجى إدخال مبلغ صحيح')
                return redirect('purchases:purchase_invoice_detail', pk=pk)

            if amount > invoice.remaining_amount:
                messages.error(request, f'❌ المبلغ أكبر من المتبقي ({invoice.remaining_amount} ج.م)')
                return redirect('purchases:purchase_invoice_detail', pk=pk)

            # تحويل التاريخ
            from datetime import datetime
            if payment_date:
                try:
                    payment_date = datetime.strptime(payment_date, '%Y-%m-%d').date()
                except ValueError:
                    payment_date = timezone.now().date()
            else:
                payment_date = timezone.now().date()

            # إنشاء الدفعة
            payment = SupplierPayment.objects.create(
                supplier=invoice.supplier,
                payment_date=payment_date,
                total_amount=amount,
                payment_method=payment_method,
                reference_number=reference_number,
                notes=notes,
                created_by=request.user
            )

            # ربط الدفعة بالفاتورة
            PaymentAllocation.objects.create(
                payment=payment,
                invoice=invoice,
                allocated_amount=amount
            )

            messages.success(request, f'✅ تم إضافة دفعة بمبلغ {amount} ج.م بنجاح!')

        except Exception as e:
            messages.error(request, f'❌ خطأ في إضافة الدفعة: {str(e)}')

    return redirect('purchases:purchase_invoice_detail', pk=pk)


@login_required
def financial_tracking(request):
    """المتابعة المالية للموردين مع التنبيهات"""
    from django.db.models import Sum, Count, Q
    from datetime import date

    # فلاتر البحث
    search = request.GET.get('search', '')
    status_filter = request.GET.get('status', '')
    sort_by = request.GET.get('sort', 'name')

    # جلب جميع الموردين
    suppliers = Supplier.objects.filter(is_active=True)

    if search:
        suppliers = suppliers.filter(name__icontains=search)

    # إحصائيات عامة
    total_invoices = PurchaseInvoice.objects.all()
    total_amount = total_invoices.aggregate(Sum('total_amount'))['total_amount__sum'] or 0
    paid_amount = total_invoices.aggregate(Sum('paid_amount_manual'))['paid_amount_manual__sum'] or 0
    pending_amount = total_amount - paid_amount

    # الفواتير المتأخرة
    today = timezone.now().date()
    overdue_invoices = PurchaseInvoice.objects.filter(
        due_date__lt=today,
        paid_amount_manual__lt=F('total_amount')
    )
    overdue_amount = sum(invoice.remaining_amount for invoice in overdue_invoices)

    # بيانات الموردين
    suppliers_data = []
    for supplier in suppliers:
        supplier_invoices = PurchaseInvoice.objects.filter(supplier=supplier)

        supplier_total = supplier_invoices.aggregate(Sum('total_amount'))['total_amount__sum'] or 0
        supplier_paid = supplier_invoices.aggregate(Sum('paid_amount_manual'))['paid_amount_manual__sum'] or 0
        supplier_pending = supplier_total - supplier_paid

        # المتأخرات للمورد
        supplier_overdue = supplier_invoices.filter(
            due_date__lt=today,
            paid_amount_manual__lt=F('total_amount')
        )
        supplier_overdue_amount = sum(invoice.remaining_amount for invoice in supplier_overdue)

        supplier_data = {
            'supplier': supplier,
            'total_amount': supplier_total,
            'paid_amount': supplier_paid,
            'pending_amount': supplier_pending,
            'remaining_amount': supplier_pending,
            'overdue_amount': supplier_overdue_amount,
            'invoices_count': supplier_invoices.count(),
            'overdue_count': supplier_overdue.count(),
        }

        # تطبيق فلتر الحالة
        if status_filter == 'paid' and supplier_pending > 0:
            continue
        elif status_filter == 'pending' and supplier_pending <= 0:
            continue
        elif status_filter == 'overdue' and supplier_overdue_amount <= 0:
            continue

        suppliers_data.append(supplier_data)

    # ترتيب النتائج
    if sort_by == 'amount':
        suppliers_data.sort(key=lambda x: x['total_amount'], reverse=True)
    elif sort_by == 'overdue':
        suppliers_data.sort(key=lambda x: x['overdue_amount'], reverse=True)
    else:
        suppliers_data.sort(key=lambda x: x['supplier'].name)

    context = {
        'suppliers_data': suppliers_data,
        'total_suppliers': len(suppliers_data),
        'total_amount': total_amount,
        'paid_amount': paid_amount,
        'pending_amount': pending_amount,
        'overdue_amount': overdue_amount,
        'overdue_invoices': overdue_invoices,
        'today': today,
    }

    return render(request, 'purchases/financial_tracking.html', context)


@login_required
def supplier_statement(request, supplier_id):
    """كشف حساب المورد"""
    supplier = get_object_or_404(Supplier, pk=supplier_id)

    # جلب جميع الفواتير والمدفوعات
    invoices = PurchaseInvoice.objects.filter(supplier=supplier).order_by('-invoice_date')

    # جلب جميع المدفوعات للمورد مباشرة
    payments = SupplierPayment.objects.filter(supplier=supplier).order_by('-payment_date')

    # حساب الرصيد الصحيح (يشمل المدفوعات من الفواتير والمدفوعات المنفصلة)
    total_invoices = invoices.aggregate(Sum('total_amount'))['total_amount__sum'] or 0

    # حساب المدفوعات من الفواتير (paid_amount)
    invoice_payments = invoices.aggregate(Sum('paid_amount'))['paid_amount__sum'] or 0

    # حساب المدفوعات المنفصلة
    separate_payments = payments.aggregate(Sum('total_amount'))['total_amount__sum'] or 0

    # إجمالي المدفوعات
    total_payments = invoice_payments + separate_payments

    # الرصيد المتبقي
    balance = total_invoices - total_payments

    print(f"🔍 حساب الرصيد للمورد {supplier.name}:")
    print(f"🔍 إجمالي الفواتير: {total_invoices}")
    print(f"🔍 مدفوعات الفواتير: {invoice_payments}")
    print(f"🔍 مدفوعات منفصلة: {separate_payments}")
    print(f"🔍 إجمالي المدفوعات: {total_payments}")
    print(f"🔍 الرصيد المتبقي: {balance}")

    context = {
        'supplier': supplier,
        'invoices': invoices,
        'payments': payments,
        'total_invoices': total_invoices,
        'invoice_payments': invoice_payments,
        'separate_payments': separate_payments,
        'total_payments': total_payments,
        'balance': balance,
    }

    return render(request, 'purchases/supplier_statement.html', context)


@login_required
def supplier_payment_simple(request, supplier_id):
    """دفع بسيط ومضمون للمورد"""
    supplier = get_object_or_404(Supplier, pk=supplier_id)

    if request.method == 'POST':
        try:
            # الحصول على البيانات
            amount_str = request.POST.get('amount', '').strip()
            payment_method = request.POST.get('payment_method', 'cash').strip()
            notes = request.POST.get('notes', '').strip()

            print(f"🔍 دفع للمورد {supplier.name} (ID: {supplier_id})")
            print(f"🔍 المبلغ المستلم: '{amount_str}'")
            print(f"🔍 طريقة الدفع: '{payment_method}'")

            # التحقق من وجود المبلغ
            if not amount_str:
                messages.error(request, '❌ يرجى إدخال المبلغ')
                return redirect('purchases:supplier_statement', supplier_id=supplier_id)

            # تحويل المبلغ
            try:
                amount = float(amount_str)
            except ValueError:
                messages.error(request, '❌ المبلغ غير صحيح')
                return redirect('purchases:supplier_statement', supplier_id=supplier_id)

            # التحقق من صحة المبلغ
            if amount <= 0:
                messages.error(request, '❌ يرجى إدخال مبلغ أكبر من صفر')
                return redirect('purchases:supplier_statement', supplier_id=supplier_id)

            # التحقق من طريقة الدفع
            if not payment_method:
                payment_method = 'cash'

            print(f"✅ المبلغ صحيح: {amount}")
            print(f"✅ طريقة الدفع: {payment_method}")

            # إنشاء الدفعة
            payment = SupplierPayment.objects.create(
                supplier=supplier,
                total_amount=amount,
                payment_method=payment_method,
                payment_date=timezone.now().date(),
                notes=notes,
                created_by=request.user
            )

            print(f"✅ تم إنشاء الدفعة بنجاح - ID: {payment.id}")

            # رسالة نجاح
            messages.success(
                request,
                f'✅ تم دفع {amount:,.2f} ج.م لـ {supplier.name} بنجاح!'
            )

        except Exception as e:
            print(f"❌ خطأ في الدفع: {str(e)}")
            messages.error(request, f'❌ خطأ في الدفع: {str(e)}')

    return redirect('purchases:supplier_statement', supplier_id=supplier_id)
