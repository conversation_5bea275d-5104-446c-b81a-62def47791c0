{% extends 'base.html' %}
{% load static %}

{% block title %}المتابعة المالية للموردين - نظام أوساريك{% endblock %}

{% block extra_css %}
<style>
    .financial-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 40px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    .stats-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border-left: 5px solid;
        transition: all 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .stats-card.total { border-left-color: #3498db; }
    .stats-card.paid { border-left-color: #27ae60; }
    .stats-card.pending { border-left-color: #f39c12; }
    .stats-card.overdue { border-left-color: #e74c3c; }

    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
    }

    .stats-label {
        color: #7f8c8d;
        font-size: 1.1rem;
        font-weight: 600;
    }

    .supplier-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .supplier-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #3498db, #27ae60, #f39c12, #e74c3c);
    }

    .supplier-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .supplier-name {
        font-size: 1.3rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 15px;
    }

    .amount-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #ecf0f1;
    }

    .amount-row:last-child {
        border-bottom: none;
        font-weight: 700;
        font-size: 1.1rem;
    }

    .amount-label {
        color: #7f8c8d;
        font-weight: 600;
    }

    .amount-value {
        font-weight: 700;
    }

    .amount-total { color: #3498db; }
    .amount-paid { color: #27ae60; }
    .amount-pending { color: #f39c12; }
    .amount-overdue { color: #e74c3c; animation: pulse 2s infinite; }

    .alert-overdue {
        background: linear-gradient(135deg, #f8d7da, #f5c6cb);
        border: 2px solid #e74c3c;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
        animation: glow 3s infinite;
    }

    @keyframes glow {
        0% { box-shadow: 0 0 5px rgba(231, 76, 60, 0.3); }
        50% { box-shadow: 0 0 20px rgba(231, 76, 60, 0.6); }
        100% { box-shadow: 0 0 5px rgba(231, 76, 60, 0.3); }
    }

    .filter-section {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .btn-action {
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        margin: 2px;
    }

    .btn-view {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        border: none;
    }

    .btn-view:hover {
        background: linear-gradient(135deg, #2980b9, #1f4e79);
        color: white;
        transform: translateY(-2px);
    }

    .btn-pay {
        background: linear-gradient(135deg, #27ae60, #229954);
        color: white;
        border: none;
    }

    .btn-pay:hover {
        background: linear-gradient(135deg, #229954, #1e7e34);
        color: white;
        transform: translateY(-2px);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- رأس الصفحة -->
    <div class="financial-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-3">
                    <i class="bi bi-graph-up-arrow"></i>
                    المتابعة المالية للموردين
                </h1>
                <p class="mb-0">تتبع المدفوعات والمستحقات مع التنبيهات الذكية</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="text-white">
                    <h3>{{ total_suppliers }}</h3>
                    <small>إجمالي الموردين</small>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card total">
                <div class="stats-number text-primary">{{ total_amount|floatformat:0 }}</div>
                <div class="stats-label">إجمالي المستحقات (ج.م)</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card paid">
                <div class="stats-number text-success">{{ paid_amount|floatformat:0 }}</div>
                <div class="stats-label">المبلغ المدفوع (ج.م)</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card pending">
                <div class="stats-number text-warning">{{ pending_amount|floatformat:0 }}</div>
                <div class="stats-label">المبلغ المعلق (ج.م)</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card overdue">
                <div class="stats-number text-danger">{{ overdue_amount|floatformat:0 }}</div>
                <div class="stats-label">المتأخرات (ج.م)</div>
            </div>
        </div>
    </div>

    <!-- تنبيهات المتأخرات -->
    {% if overdue_invoices %}
    <div class="alert-overdue">
        <h5><i class="bi bi-exclamation-triangle"></i> تنبيه: فواتير متأخرة!</h5>
        <p class="mb-2">يوجد {{ overdue_invoices|length }} فاتورة متأخرة عن موعد الاستحقاق</p>
        <small>إجمالي المتأخرات: {{ overdue_amount|floatformat:2 }} ج.م</small>
    </div>
    {% endif %}

    <!-- فلاتر البحث -->
    <div class="filter-section">
        <form method="get" class="row align-items-end">
            <div class="col-md-3">
                <label class="form-label">البحث بالمورد</label>
                <input type="text" name="search" class="form-control" value="{{ request.GET.search }}" 
                       placeholder="اسم المورد">
            </div>
            <div class="col-md-2">
                <label class="form-label">الحالة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="paid" {% if request.GET.status == 'paid' %}selected{% endif %}>مدفوعة</option>
                    <option value="pending" {% if request.GET.status == 'pending' %}selected{% endif %}>معلقة</option>
                    <option value="overdue" {% if request.GET.status == 'overdue' %}selected{% endif %}>متأخرة</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">الترتيب</label>
                <select name="sort" class="form-select">
                    <option value="name">الاسم</option>
                    <option value="amount" {% if request.GET.sort == 'amount' %}selected{% endif %}>المبلغ</option>
                    <option value="overdue" {% if request.GET.sort == 'overdue' %}selected{% endif %}>المتأخرات</option>
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="bi bi-search"></i> بحث
                </button>
            </div>
            <div class="col-md-3 text-end">
                <a href="{% url 'purchases:financial_tracking' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-clockwise"></i> إعادة تعيين
                </a>
            </div>
        </form>
    </div>

    <!-- قائمة الموردين -->
    <div class="row">
        {% for supplier_data in suppliers_data %}
        <div class="col-md-6 col-lg-4">
            <div class="supplier-card">
                <div class="supplier-name">
                    <i class="bi bi-building"></i>
                    {{ supplier_data.supplier.name }}
                </div>
                
                <div class="amount-row">
                    <span class="amount-label">إجمالي الفواتير:</span>
                    <span class="amount-value amount-total">{{ supplier_data.total_amount|floatformat:2 }} ج.م</span>
                </div>
                
                <div class="amount-row">
                    <span class="amount-label">المبلغ المدفوع:</span>
                    <span class="amount-value amount-paid">{{ supplier_data.paid_amount|floatformat:2 }} ج.م</span>
                </div>
                
                <div class="amount-row">
                    <span class="amount-label">المبلغ المعلق:</span>
                    <span class="amount-value amount-pending">{{ supplier_data.pending_amount|floatformat:2 }} ج.م</span>
                </div>
                
                {% if supplier_data.overdue_amount > 0 %}
                <div class="amount-row">
                    <span class="amount-label">المتأخرات:</span>
                    <span class="amount-value amount-overdue">{{ supplier_data.overdue_amount|floatformat:2 }} ج.م</span>
                </div>
                {% endif %}
                
                <div class="amount-row">
                    <span class="amount-label">المتبقي:</span>
                    <span class="amount-value amount-pending">{{ supplier_data.remaining_amount|floatformat:2 }} ج.م</span>
                </div>

                <div class="text-center mt-3">
                    <a href="{% url 'purchases:supplier_detail' supplier_data.supplier.pk %}" class="btn-action btn-view">
                        <i class="bi bi-eye"></i> عرض التفاصيل
                    </a>
                    {% if supplier_data.remaining_amount > 0 %}
                    <a href="{% url 'purchases:supplier_payments' supplier_data.supplier.pk %}" class="btn-action btn-pay">
                        <i class="bi bi-credit-card"></i> إدارة المدفوعات
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center text-muted py-5">
                <i class="bi bi-inbox display-4"></i>
                <p class="mt-3">لا توجد بيانات مالية</p>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}
